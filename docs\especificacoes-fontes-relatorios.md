# Especificações de Fontes para Relatórios e Layouts

## 📋 Visão Geral
Este documento define os padrões de tipografia e layout para todos os relatórios do sistema RLPONTO-WEB, garantindo consistência visual e legibilidade otimizada.

## 🎯 Padrão Base
- **Fonte Principal**: Segoe UI, Tahoma, Geneva, Verdana, sans-serif
- **Tamanho Base**: 11pt (padrão Microsoft Word)
- **Entrelinhamento**: 1.4 (140%)
- **Unidade de Medida**: pt (pontos) para compatibilidade com impressão

## 📊 Hierarquia Tipográfica

### Títulos Principais (H1)
- **Tamanho**: 18pt
- **Peso**: 600 (Semi-bold)
- **Uso**: Título principal do relatório
- **Impressão**: 14pt

### Subtítulos (H2/H3)
- **Tamanho**: 14pt
- **Peso**: 600 (Semi-bold)
- **Uso**: Seções do relatório
- **Impressão**: 12pt

### Informações da Empresa
- **Tamanho**: 12pt
- **Peso**: 400 (Normal)
- **Uso**: Nome da empresa, dados corporativos
- **Impressão**: 11pt

### Data de Geração
- **Tamanho**: 10pt
- **Peso**: 400 (Normal)
- **Uso**: Timestamp do relatório
- **Impressão**: 9pt

## 📋 Tabelas

### Cabeçalhos (thead th)
- **Tamanho**: 9pt
- **Peso**: 600 (Semi-bold)
- **Transformação**: UPPERCASE
- **Espaçamento**: 0.5px entre letras
- **Padding**: 12px (tela) / 6px (impressão)

### Conteúdo (tbody td)
- **Tamanho**: 9pt (dados de funcionários) / 11pt (outros relatórios)
- **Peso**: 400 (Normal)
- **Padding**: 10px (tela) / 6px (impressão)
- **Alinhamento**: Vertical middle

### Badges de Status
- **Tamanho**: 9pt
- **Peso**: 600 (Semi-bold)
- **Transformação**: UPPERCASE
- **Padding**: 4px 10px
- **Border-radius**: 15px

## 📱 Cards de Resumo

### Números Principais
- **Tamanho**: 16pt
- **Peso**: 700 (Bold)
- **Uso**: Estatísticas principais

### Descrições
- **Tamanho**: 10pt
- **Peso**: 400 (Normal)
- **Opacidade**: 90%

## � Relatórios Específicos

### Relatório de Funcionários
- **Tabela de dados**: 9pt (fonte reduzida para melhor densidade)
- **Cabeçalho da empresa**: Inclui CNPJ obrigatoriamente
- **Layout**: Minimalista com foco nos dados essenciais
- **Estrutura do cabeçalho**:
  - Nome fantasia ou razão social
  - CNPJ da empresa
  - Data e hora de geração

## �🔘 Botões de Ação

### Botões de Impressão/PDF
- **Tamanho**: 10pt
- **Peso**: 400 (Normal)
- **Padding**: 8px 15px
- **Border-radius**: 25px

## 📄 Rodapé

### Informações do Sistema
- **Tamanho**: 10pt (tela) / 9pt (impressão)
- **Peso**: 400 (Normal)
- **Cor**: #6c757d
- **Alinhamento**: Centro

## 🖨️ Especificações para Impressão

### Ajustes Automáticos
- **Redução geral**: -2pt a -3pt em relação à versão web
- **Background**: Branco puro
- **Cores**: Tons de cinza para economia
- **Padding**: Reduzido em 30-40%

### Elementos Ocultos na Impressão
- Cards de resumo estatístico
- Botões de ação
- Gradientes e sombras
- Animações

## 🎨 Paleta de Cores

### Cores Principais (Web)
- **Primária**: #667eea
- **Secundária**: #764ba2
- **Sucesso**: #155724 (fundo: #d4edda)
- **Erro**: #721c24 (fundo: #f8d7da)

### Cores para Impressão
- **Texto Principal**: #333333
- **Texto Secundário**: #666666
- **Bordas**: #333333
- **Fundo Alternativo**: #f8f9fa

## 📐 Espaçamentos Padrão

### Margens e Padding
- **Pequeno**: 8px
- **Médio**: 12px
- **Grande**: 20px
- **Extra Grande**: 30px

### Impressão (Reduzidos)
- **Pequeno**: 4px
- **Médio**: 6px
- **Grande**: 10px
- **Extra Grande**: 15px

## 📱 Responsividade

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Ajustes Mobile
- **Título Principal**: 16pt
- **Texto Base**: 11pt
- **Padding**: Reduzido em 25%

## 🔧 Implementação Técnica

### CSS Classes Padrão
```css
.report-title { font-size: 18pt; font-weight: 600; }
.report-subtitle { font-size: 14pt; font-weight: 600; }
.report-body { font-size: 11pt; line-height: 1.4; }
.report-small { font-size: 10pt; }
.report-tiny { font-size: 9pt; }
```

### Media Queries
```css
@media print {
  .report-title { font-size: 14pt !important; }
  .report-body { font-size: 11pt !important; }
}

@media (max-width: 768px) {
  .report-title { font-size: 16pt; }
}
```

## ✅ Checklist de Implementação

### Para Novos Relatórios
- [ ] Fonte base 11pt aplicada
- [ ] Hierarquia tipográfica seguida
- [ ] Versão de impressão otimizada
- [ ] Responsividade testada
- [ ] Cores de impressão ajustadas
- [ ] Espaçamentos padronizados

### Testes Obrigatórios
- [ ] Visualização em tela (desktop)
- [ ] Visualização em mobile
- [ ] Pré-visualização de impressão
- [ ] Exportação para PDF
- [ ] Legibilidade em diferentes resoluções

## 📚 Referências

### Padrões Seguidos
- **Microsoft Word**: Fonte padrão 11pt
- **Google Docs**: Entrelinhamento 1.4
- **Adobe**: Hierarquia tipográfica
- **Material Design**: Espaçamentos e responsividade

### Ferramentas de Teste
- **Chrome DevTools**: Responsividade
- **Print Preview**: Visualização de impressão
- **PDF Viewers**: Qualidade de exportação

---

**Última Atualização**: 07/07/2025  
**Versão**: 1.0  
**Responsável**: Sistema RLPONTO-WEB
