{% extends "base.html" %}

{% block title %}{{ titulo }}{% endblock %}

{% block extra_css %}
<style>
    .registro-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .status-card {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    }
    
    .status-card h2 {
        margin: 0 0 10px 0;
        font-size: 2rem;
    }
    
    .status-card .info {
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    .biometria-area {
        background: white;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .biometria-icon {
        width: 120px;
        height: 120px;
        margin: 0 auto 20px;
        background: #f8f9fa;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid #007bff;
        transition: all 0.3s ease;
    }
    
    .biometria-icon.ativo {
        border-color: #28a745;
        background: #d4edda;
        animation: pulse 2s infinite;
    }
    
    .biometria-icon.erro {
        border-color: #dc3545;
        background: #f8d7da;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .biometria-icon i {
        font-size: 3rem;
        color: #007bff;
    }
    
    .biometria-icon.ativo i {
        color: #28a745;
    }
    
    .biometria-icon.erro i {
        color: #dc3545;
    }
    
    .status-texto {
        font-size: 1.2rem;
        margin: 20px 0;
        font-weight: 500;
    }
    
    .status-aguardando { color: #007bff; }
    .status-sucesso { color: #28a745; }
    .status-erro { color: #dc3545; }
    
    .instrucoes {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .instrucoes h5 {
        color: #495057;
        margin-bottom: 15px;
    }
    
    .instrucoes ul {
        margin: 0;
        padding-left: 20px;
    }
    
    .instrucoes li {
        margin: 5px 0;
        color: #6c757d;
    }
    
    .tipos-registro {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }
    
    .tipo-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .tipo-btn:hover {
        border-color: #007bff;
        background: #f8f9fa;
    }
    
    .tipo-btn.selecionado {
        border-color: #007bff;
        background: #007bff;
        color: white;
    }
    
    .controles {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
    }
    
    .btn-acao {
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: 500;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 150px;
    }
    
    .btn-iniciar {
        background: #28a745;
        color: white;
    }
    
    .btn-iniciar:hover {
        background: #218838;
    }
    
    .btn-parar {
        background: #dc3545;
        color: white;
    }
    
    .btn-parar:hover {
        background: #c82333;
    }
    
    .btn-limpar {
        background: #6c757d;
        color: white;
    }
    
    .btn-limpar:hover {
        background: #5a6268;
    }
    
    .resultado {
        margin-top: 20px;
        padding: 15px;
        border-radius: 8px;
        display: none;
    }
    
    .resultado.sucesso {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .resultado.erro {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 10px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="registro-container">
    <!-- Header com informações atuais -->
    <div class="status-card">
        <h2>{{ titulo }}</h2>
        <div class="info">
            <i class="fas fa-user"></i> {{ usuario_logado }} |
            <i class="fas fa-calendar"></i> {{ data_atual }} |
            <i class="fas fa-clock"></i> <span id="hora-atual">{{ hora_atual }}</span>
        </div>
    </div>

    <!-- Área principal de biometria -->
    <div class="biometria-area">
        <div class="biometria-icon" id="biometria-icon">
            <i class="fas fa-fingerprint"></i>
        </div>
        
        <div class="status-texto status-aguardando" id="status-texto">
            Aguardando seleção do tipo de registro
        </div>
        
        <!-- Seleção do tipo de registro -->
        <div class="tipos-registro" id="tipos-registro">
            <div class="tipo-btn" data-tipo="entrada_manha">
                <i class="fas fa-sun"></i><br>
                <strong>Entrada Manhã</strong><br>
                <small>08:00</small>
            </div>
            <div class="tipo-btn" data-tipo="saida_almoco">
                <i class="fas fa-utensils"></i><br>
                <strong>Saída Almoço</strong><br>
                <small>12:00</small>
            </div>
            <div class="tipo-btn" data-tipo="entrada_tarde">
                <i class="fas fa-sun"></i><br>
                                        <strong>Retorno Almoço</strong><br>
                <small>13:00</small>
            </div>
            <div class="tipo-btn" data-tipo="saida">
                <i class="fas fa-moon"></i><br>
                <strong>Saída</strong><br>
                <small>17:00</small>
            </div>
        </div>
        
        <!-- Instruções -->
        <div class="instrucoes">
            <h5><i class="fas fa-info-circle"></i> Instruções:</h5>
            <ul>
                <li>Selecione o tipo de registro desejado</li>
                <li>Clique em "Iniciar Captura" para ativar o leitor biométrico</li>
                <li>Posicione o dedo no leitor ZK4500</li>
                <li>Mantenha o dedo firme até a captura ser concluída</li>
                <li>Aguarde a confirmação do registro</li>
            </ul>
        </div>
        
        <!-- Controles -->
        <div class="controles">
            <button type="button" class="btn-acao btn-iniciar" id="btn-iniciar" disabled>
                Iniciar Captura
            </button>
            <button type="button" class="btn-acao btn-parar" id="btn-parar" style="display:none;">
                Parar Captura
            </button>
            <button type="button" class="btn-acao btn-limpar" id="btn-limpar">
                Limpar
            </button>
        </div>
        
        <!-- Resultado -->
        <div class="resultado" id="resultado"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elementos da interface
    const tiposRegistro = document.querySelectorAll('.tipo-btn');
    const btnIniciar = document.getElementById('btn-iniciar');
    const btnParar = document.getElementById('btn-parar');
    const btnLimpar = document.getElementById('btn-limpar');
    const statusTexto = document.getElementById('status-texto');
    const biometriaIcon = document.getElementById('biometria-icon');
    const resultado = document.getElementById('resultado');
    
    let tipoSelecionado = null;
    let capturaAtiva = false;
    
    // Atualizar hora atual
    setInterval(function() {
        const agora = new Date();
        const horaFormatada = agora.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });
        document.getElementById('hora-atual').textContent = horaFormatada;
    }, 1000);
    
    // Seleção do tipo de registro
    tiposRegistro.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remover seleção anterior
            tiposRegistro.forEach(b => b.classList.remove('selecionado'));
            
            // Selecionar atual
            this.classList.add('selecionado');
            tipoSelecionado = this.dataset.tipo;
            
            // Habilitar botão iniciar
            btnIniciar.disabled = false;
            
            // Atualizar status
            const tipoTexto = this.querySelector('strong').textContent;
            statusTexto.textContent = `Tipo selecionado: ${tipoTexto}. Clique em "Iniciar Captura"`;
            statusTexto.className = 'status-texto status-aguardando';
        });
    });
    
    // Iniciar captura biométrica
    btnIniciar.addEventListener('click', function() {
        if (!tipoSelecionado) {
            alert('Selecione um tipo de registro primeiro!');
            return;
        }
        
        iniciarCapturaBiometrica();
    });
    
    // Parar captura
    btnParar.addEventListener('click', function() {
        pararCapturaBiometrica();
    });
    
    // Limpar seleção
    btnLimpar.addEventListener('click', function() {
        limparSelecao();
    });
    
    function iniciarCapturaBiometrica() {
        capturaAtiva = true;
        
        // Atualizar interface
        btnIniciar.style.display = 'none';
        btnParar.style.display = 'inline-block';
        
        biometriaIcon.classList.add('ativo');
        statusTexto.innerHTML = '<span class="loading"></span>Posicione o dedo no leitor biométrico...';
        statusTexto.className = 'status-texto status-aguardando';
        
        resultado.style.display = 'none';
        
        // Simular comunicação com ZKAgent (substituir por implementação real)
        // Esta parte seria integrada com o JavaScript do ZKAgent existente
        setTimeout(() => {
            if (capturaAtiva) {
                simularCapturaComSucesso();
            }
        }, 3000);
    }
    
    function pararCapturaBiometrica() {
        capturaAtiva = false;
        
        // Resetar interface
        btnIniciar.style.display = 'inline-block';
        btnParar.style.display = 'none';
        
        biometriaIcon.classList.remove('ativo');
        statusTexto.textContent = 'Captura interrompida. Selecione um tipo e tente novamente.';
        statusTexto.className = 'status-texto';
    }
    
    function simularCapturaComSucesso() {
        if (!capturaAtiva) return;
        
        // Simular registro no banco
        const dadosRegistro = {
            funcionario_id: 1, // Este valor viria da autenticação biométrica real
            tipo_registro: tipoSelecionado,
            qualidade_biometria: 85
        };
        
        // Enviar para API
        fetch('/registro-ponto/api/registrar-biometrico', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(dadosRegistro)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarSucesso(data.message, data);
            } else {
                mostrarErro(data.message);
            }
        })
        .catch(error => {
            mostrarErro('Erro de comunicação: ' + error.message);
        });
    }
    
    function mostrarSucesso(mensagem, dados) {
        capturaAtiva = false;
        
        // Atualizar interface
        btnIniciar.style.display = 'inline-block';
        btnParar.style.display = 'none';
        
        biometriaIcon.classList.remove('ativo');
        biometriaIcon.classList.add('sucesso');
        
        statusTexto.innerHTML = '<i class="fas fa-check-circle"></i> Registro realizado com sucesso!';
        statusTexto.className = 'status-texto status-sucesso';
        
        // Mostrar resultado
        resultado.innerHTML = `
            <strong>Ponto registrado com sucesso!</strong><br>
            <i class="fas fa-user"></i> ${dados.funcionario_nome || 'Funcionário'}<br>
            <i class="fas fa-clock"></i> ${dados.data_hora}<br>
            <i class="fas fa-fingerprint"></i> Registro biométrico
        `;
        resultado.className = 'resultado sucesso';
        resultado.style.display = 'block';
        
        // Limpar automaticamente após 5 segundos
        setTimeout(limparSelecao, 5000);
    }
    
    function mostrarErro(mensagem) {
        capturaAtiva = false;
        
        // Atualizar interface
        btnIniciar.style.display = 'inline-block';
        btnParar.style.display = 'none';
        
        biometriaIcon.classList.remove('ativo');
        biometriaIcon.classList.add('erro');
        
        statusTexto.innerHTML = '<i class="fas fa-times-circle"></i> ' + mensagem;
        statusTexto.className = 'status-texto status-erro';
        
        // Mostrar resultado
        resultado.innerHTML = `<strong>Erro:</strong> ${mensagem}`;
        resultado.className = 'resultado erro';
        resultado.style.display = 'block';
    }
    
    function limparSelecao() {
        capturaAtiva = false;
        tipoSelecionado = null;
        
        // Resetar seleção
        tiposRegistro.forEach(btn => btn.classList.remove('selecionado'));
        
        // Resetar interface
        btnIniciar.style.display = 'inline-block';
        btnIniciar.disabled = true;
        btnParar.style.display = 'none';
        
        biometriaIcon.className = 'biometria-icon';
        statusTexto.textContent = 'Aguardando seleção do tipo de registro';
        statusTexto.className = 'status-texto status-aguardando';
        
        resultado.style.display = 'none';
    }
});
</script>
{% endblock %} 