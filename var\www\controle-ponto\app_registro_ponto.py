# ========================================
# BLUEPRINT REGISTRO DE PONTO - RLPONTO-WEB
# Data: 08/01/2025
# Descrição: Sistema de registro de ponto biométrico e manual
# ========================================

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, time, timedelta
from utils.auth import require_login
from utils.database import get_db_connection
from utils.helpers import mascarar_cpf, validar_cpf_formato, calcular_horas_trabalhadas
from pymysql.cursors import DictCursor
import logging
import ipaddress
import ntplib
import time as systime

# Configurar logging primeiro
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Importar novas regras de controle de jornada
try:
    from app_controle_jornada import (
        validar_entrada_manha_nova_regra,
        validar_intervalo_almoco_nova_regra,
        validar_saida_expediente_nova_regra,
        processar_banco_horas_dia
    )
    NOVAS_REGRAS_ATIVAS = True
    logger.info("✅ Novas regras de controle de jornada carregadas")
except ImportError as e:
    NOVAS_REGRAS_ATIVAS = False
    logger.warning(f"⚠️ Novas regras de controle de jornada não disponíveis: {e}")

# Criar Blueprint - Aceitando tanto hífen quanto underline para compatibilidade
registro_ponto_bp = Blueprint('registro_ponto', __name__, url_prefix='/registro-ponto')

# Criar um segundo blueprint com underscore para compatibilidade
registro_ponto_underscore_bp = Blueprint('registro_ponto_underscore', __name__, url_prefix='/registro_ponto')

# ========================================
# FUNÇÕES AUXILIARES
# ========================================

def normalizar_caminho_foto(foto_3x4):
    """
    Normaliza o caminho da foto para garantir que sempre comece com /static/
    
    Args:
        foto_3x4 (str): Caminho da foto do banco de dados
        
    Returns:
        str or None: Caminho normalizado ou None se não houver foto válida
    """
    if not foto_3x4:
        return None
    
    # Ignorar o placeholder padrão
    if foto_3x4 == '/static/images/funcionario_sem_foto.svg':
        return None
    
    # Se já começa com /static/, retornar como está
    if foto_3x4.startswith('/static/'):
        return foto_3x4
    
    # Se não começa com /static/, adicionar o prefixo
    if foto_3x4.startswith('static/'):
        return '/' + foto_3x4
    else:
        return '/static/' + foto_3x4

def validar_duplicata_registro(funcionario_id, tipo_registro, data_atual):
    """
    Verifica se já existe um registro do mesmo tipo para o funcionário na data atual.
    Também verifica se a sequência lógica dos registros está sendo respeitada.
    
    Args:
        funcionario_id (int): ID do funcionário
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        data_atual (date): Data para verificação
    
    Returns:
        dict: Resultado com existência de duplicata e mensagem
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # ✅ MELHORIA: Verificar a data de cadastro do funcionário
        cursor.execute("""
            SELECT 
                DATE(data_cadastro) AS data_cadastro, 
                nome_completo
            FROM funcionarios
            WHERE id = %s AND ativo = TRUE
        """, (funcionario_id,))
        
        info_funcionario = cursor.fetchone()
        
        if not info_funcionario:
            return {
                'existe': True,
                'mensagem': f"Funcionário ID {funcionario_id} não encontrado ou está inativo",
                'registro_existente': None
            }
        
        # ✅ MELHORIA: Se a data atual for igual à data de cadastro, verificar se foi cadastrado à tarde
        # Nesse caso, ignorar entradas anteriores ao cadastro
        data_cadastro = info_funcionario['data_cadastro'].date() if hasattr(info_funcionario['data_cadastro'], 'date') else info_funcionario['data_cadastro']
        
        # Verificar duplicata no mesmo dia
        data_inicio = data_atual.strftime('%Y-%m-%d 00:00:00')
        data_fim = data_atual.strftime('%Y-%m-%d 23:59:59')
        
        # ✅ CORREÇÃO: Buscar mais informações sobre o registro existente
        cursor.execute("""
            SELECT 
                id, 
                tipo_registro, 
                DATE_FORMAT(data_hora, '%H:%i:%s') as hora_registro,
                DATE_FORMAT(data_hora, '%d/%m/%Y %H:%i:%s') as data_hora_completa,
                metodo_registro,
                observacoes,
                IFNULL(status_pontualidade, 'Registrado') as status_pontualidade
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND data_hora BETWEEN %s AND %s
            ORDER BY data_hora ASC
        """, (funcionario_id, data_inicio, data_fim))
        
        registros_do_dia = cursor.fetchall()
        
        # Verificar duplicata do mesmo tipo
        for registro in registros_do_dia:
            if registro['tipo_registro'] == tipo_registro:
                # ✅ CORREÇÃO: Formatar tipo de registro para a mensagem
                tipo_formatado = tipo_registro.replace('_', ' ').title()
                
                # ✅ CORREÇÃO: Retornar informações mais detalhadas
                return {
                    'existe': True, 
                    'mensagem': f"Já existe um registro de {tipo_formatado} para hoje às {registro['hora_registro']} ({registro['status_pontualidade']})",
                    'registro_existente': {
                        'id': registro['id'],
                        'hora': registro['hora_registro'],
                        'data_hora_completa': registro['data_hora_completa'],
                        'status_pontualidade': registro['status_pontualidade'],
                        'metodo_registro': registro['metodo_registro'],
                        'observacoes': registro['observacoes'] if registro['observacoes'] else None
                    }
                }
        
        # ✅ MELHORIA 1: Considerar jornada a partir do dia seguinte ao cadastro
        if data_cadastro == data_atual:
            # Se estamos no mesmo dia do cadastro, não exigir registros anteriores
            # Isso evita marcar como ausência pontos anteriores ao cadastro
            logger.info(f"Funcionário {info_funcionario['nome_completo']} cadastrado hoje. Ignorando verificação de sequência para primeiro dia.")
            
            # Nesse caso, retornamos sem verificação de sequência
            conn.close()
            return {'existe': False, 'mensagem': '', 'registro_existente': None}
        
        # ✅ MELHORIA 2: Tornar a verificação de sequência lógica opcional
        # A verificação de sequência agora só acontece se o parâmetro VERIFICAR_SEQUENCIA for True
        # Vamos criar uma variável no escopo global para controlar isso
        VERIFICAR_SEQUENCIA = False  # Definido como False para remover dependência entre registros
        
        if VERIFICAR_SEQUENCIA:
            # A ordem correta é: entrada_manha -> saida_almoco -> entrada_tarde -> saida
            sequencia_correta = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
            
            # Verificar qual é a posição do tipo de registro na sequência
            if tipo_registro in sequencia_correta:
                indice_atual = sequencia_correta.index(tipo_registro)
                
                # Se não for o primeiro registro da sequência, verificar se o anterior foi registrado
                if indice_atual > 0:
                    tipo_anterior = sequencia_correta[indice_atual - 1]
                    tipo_anterior_registrado = False
                    
                    for registro in registros_do_dia:
                        if registro['tipo_registro'] == tipo_anterior:
                            tipo_anterior_registrado = True
                            break
                    
                    if not tipo_anterior_registrado:
                        tipo_anterior_formatado = tipo_anterior.replace('_', ' ').title()
                        conn.close()
                        return {
                            'existe': True,
                            'mensagem': f"Não é possível registrar {tipo_registro.replace('_', ' ').title()} sem antes registrar {tipo_anterior_formatado}",
                            'registro_existente': None
                        }
                
                # Verificar se há registros posteriores na sequência que já foram feitos
                if indice_atual < len(sequencia_correta) - 1:
                    for i in range(indice_atual + 1, len(sequencia_correta)):
                        tipo_posterior = sequencia_correta[i]
                        
                        for registro in registros_do_dia:
                            if registro['tipo_registro'] == tipo_posterior:
                                tipo_posterior_formatado = tipo_posterior.replace('_', ' ').title()
                                conn.close()
                                return {
                                    'existe': True,
                                    'mensagem': f"Não é possível registrar {tipo_registro.replace('_', ' ').title()} pois já existe um registro posterior ({tipo_posterior_formatado}) hoje",
                                    'registro_existente': None
                                }
        
        conn.close()
        # Nenhuma duplicata ou problema de sequência encontrado
        return {'existe': False, 'mensagem': '', 'registro_existente': None}
        
    except Exception as e:
        logger.error(f"Erro ao validar duplicata de registro: {str(e)}")
        return {'existe': False, 'mensagem': '', 'registro_existente': None}

def obter_horarios_funcionario(funcionario_id):
    """
    Obtém os horários de trabalho configurados para um funcionário.
    
    Args:
        funcionario_id (int): ID do funcionário
        
    Returns:
        dict: Horários configurados ou horários padrão
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # ✅ BUSCAR HORÁRIOS REAIS DO FUNCIONÁRIO
        cursor.execute("""
            SELECT 
                nome_completo,
                jornada_seg_qui_entrada,
                jornada_seg_qui_saida,
                jornada_sex_entrada,
                jornada_sex_saida,
                jornada_intervalo_entrada,
                jornada_intervalo_saida,
                tolerancia_ponto
            FROM funcionarios 
            WHERE id = %s AND ativo = TRUE
        """, (funcionario_id,))
        
        resultado = cursor.fetchone()
        conn.close()
        
        if not resultado:
            raise Exception(f"Funcionário {funcionario_id} não encontrado ou inativo")
        
        # ✅ CONVERTER TIMEDELTA PARA STRING FORMATADA
        def converter_tempo(tempo_obj):
            if tempo_obj is None:
                return None
            
            # Se for timedelta, converter para horas:minutos
            if hasattr(tempo_obj, 'total_seconds'):
                total_seconds = int(tempo_obj.total_seconds())
                horas = total_seconds // 3600
                minutos = (total_seconds % 3600) // 60
                return f"{horas:02d}:{minutos:02d}"
            
            # Se já for string no formato time, converter
            if hasattr(tempo_obj, 'strftime'):
                return tempo_obj.strftime('%H:%M')
            
            # Se já for string, retornar como está
            if isinstance(tempo_obj, str):
                return tempo_obj
            
            return str(tempo_obj)
        
        # ✅ MONTAR HORÁRIOS REAIS BASEADOS NO CADASTRO
        horarios_reais = {
            'entrada_manha': converter_tempo(resultado['jornada_seg_qui_entrada']),
            'saida_almoco': converter_tempo(resultado['jornada_intervalo_entrada']),  # Início do intervalo = saída almoço
            'entrada_tarde': converter_tempo(resultado['jornada_intervalo_saida']),   # Fim do intervalo = entrada tarde
            'saida': converter_tempo(resultado['jornada_seg_qui_saida']),
            'tolerancia_minutos': resultado['tolerancia_ponto'] or 10,
            'nome_horario': 'Horário Administrativo'
        }
        
        logger.info(f"[HORARIOS] Horários obtidos para funcionário {funcionario_id}: {horarios_reais}")
        return horarios_reais
            
    except Exception as e:
        logger.error(f"Erro ao obter horários do funcionário {funcionario_id}: {str(e)}")
        # ✅ RETORNAR HORÁRIOS PADRÃO EM CASO DE ERRO
        return {
            'entrada_manha': '07:00',
            'saida_almoco': '12:00',
            'entrada_tarde': '13:00',
            'saida': '17:00',
            'tolerancia_minutos': 10,
            'nome_horario': 'Padrão (Erro)'
        }

def registrar_ponto_no_banco(funcionario_id, tipo_registro, metodo_registro, observacoes=None, qualidade_biometria=None, status_pontualidade=None):
    """
    Registra um ponto no banco de dados.
    
    Args:
        funcionario_id (int): ID do funcionário
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        metodo_registro (str): Método de registro (biometrico, manual)
        observacoes (str, optional): Observações adicionais
        qualidade_biometria (int, optional): Qualidade da biometria (0-100)
        status_pontualidade (str, optional): Status da pontualidade (Pontual, Atrasado)
        
    Returns:
        dict: Resultado da operação
    """
    try:
        # Garantir que a tabela existe
        verificar_e_criar_tabela_registros()
        
        # Obter conexão com o banco
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter usuário atual (para registro manual)
        usuario_id = None
        if metodo_registro == 'manual' and 'usuario_id' in session:
            usuario_id = session.get('usuario_id')
        
        # Obter endereço IP e user agent
        ip_origem = request.remote_addr if request else None
        user_agent = request.headers.get('User-Agent') if request else None
        
        # ✅ CORREÇÃO: Garantir que status_pontualidade seja 'Pontual' ou 'Atrasado'
        if status_pontualidade not in ['Pontual', 'Atrasado']:
            status_pontualidade = 'Pontual'  # Valor padrão

        # ✅ CORREÇÃO: Validar observações para evitar problemas no banco
        if observacoes and len(observacoes) > 1000:  # Limitar tamanho para evitar problemas
            observacoes = observacoes[:1000]
        
        # ✅ NOVA LÓGICA: Aplicar validações das novas regras se disponíveis
        if NOVAS_REGRAS_ATIVAS and not status_pontualidade:
            try:
                hora_atual = datetime.now()

                # Validar entrada da manhã com novas regras
                if tipo_registro == 'entrada_manha':
                    validacao = validar_entrada_manha_nova_regra(funcionario_id, hora_atual)
                    if validacao['status'] == 'atrasado':
                        status_pontualidade = 'Atrasado'
                        if not observacoes:
                            observacoes = f"Atraso de {validacao['minutos_atraso']} minutos"
                    elif validacao['status'] == 'pontual':
                        status_pontualidade = 'Pontual'
                    elif validacao['status'] == 'ausente_manha':
                        status_pontualidade = 'Atrasado'
                        if not observacoes:
                            observacoes = "Registro após período da manhã"

                # Validar saída com novas regras
                elif tipo_registro == 'saida':
                    validacao = validar_saida_expediente_nova_regra(funcionario_id, hora_atual)
                    if validacao['status'] == 'saida_antecipada':
                        status_pontualidade = 'Atrasado'  # Saída antecipada como irregularidade
                        if not observacoes:
                            observacoes = f"Saída antecipada em {validacao['minutos_antecipacao']} minutos"
                    else:
                        status_pontualidade = 'Pontual'

                # Para outros tipos, manter lógica padrão
                else:
                    status_pontualidade = status_pontualidade or 'Pontual'

            except Exception as e:
                logger.warning(f"Erro ao aplicar novas regras: {e}")
                status_pontualidade = status_pontualidade or 'Pontual'
        else:
            status_pontualidade = status_pontualidade or 'Pontual'

        # Inserir registro na tabela
        cursor.execute("""
            INSERT INTO registros_ponto
            (funcionario_id, tipo_registro, data_hora, metodo_registro, criado_por,
             template_biometrico, qualidade_biometria, observacoes, ip_origem, user_agent,
             status_pontualidade)
            VALUES (%s, %s, NOW(), %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            funcionario_id,
            tipo_registro,
            metodo_registro,
            usuario_id,
            None,  # template_biometrico
            qualidade_biometria,
            observacoes,
            ip_origem,
            user_agent,
            status_pontualidade
        ))
        
        registro_id = cursor.lastrowid
        conn.commit()

        # Log do registro
        logger.info(f"[REGISTRO] Funcionário: {funcionario_id}, Tipo: {tipo_registro}, Método: {metodo_registro}, Status: {status_pontualidade}")

        conn.close()

        # ✅ NOVA FUNCIONALIDADE: Processar banco de horas automaticamente
        if NOVAS_REGRAS_ATIVAS:
            try:
                data_atual = date.today()
                sucesso_banco = processar_banco_horas_dia(funcionario_id, data_atual)
                if sucesso_banco:
                    logger.info(f"✅ Banco de horas processado para funcionário {funcionario_id} em {data_atual}")
                else:
                    logger.warning(f"⚠️ Falha ao processar banco de horas para funcionário {funcionario_id}")
            except Exception as e:
                logger.error(f"❌ Erro ao processar banco de horas: {e}")

        return {
            'success': True,
            'registro_id': registro_id,
            'message': 'Registro salvo com sucesso'
        }
    
    except Exception as e:
        logger.error(f"Erro ao registrar ponto no banco: {str(e)}")
        return {
            'success': False,
            'message': f'Erro ao registrar ponto: {str(e)}'
        }

def verificar_e_criar_tabela_registros():
    """
    Verifica se a tabela registros_ponto existe e cria se necessário.
    Também verifica se a coluna status_pontualidade existe e adiciona se necessário.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Verificar se a tabela existe
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables 
            WHERE table_schema = 'controle_ponto' 
            AND table_name = 'registros_ponto'
        """)
        
        result = cursor.fetchone()
        existe = result['COUNT(*)'] > 0
        
        if not existe:
            logger.warning("[CORREÇÃO] Tabela registros_ponto não existe. Criando...")
            
            # Criar a tabela com a coluna status_pontualidade
            cursor.execute("""
                CREATE TABLE registros_ponto (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    funcionario_id INT NOT NULL,
                    tipo_registro ENUM('entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida') NOT NULL,
                    metodo_registro ENUM('biometrico', 'manual') NOT NULL,
                    data_hora DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    qualidade_biometria INT NULL,
                    observacoes TEXT NULL,
                    ip_origem VARCHAR(45) NULL,
                    criado_por INT NULL,
                    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status_pontualidade ENUM('Pontual', 'Atrasado') NULL COMMENT 'Status de pontualidade do registro',
                    INDEX idx_funcionario_data (funcionario_id, data_hora),
                    INDEX idx_tipo_registro (tipo_registro),
                    INDEX idx_metodo_registro (metodo_registro),
                    INDEX idx_status_pontualidade (status_pontualidade),
                    FOREIGN KEY (funcionario_id) REFERENCES funcionarios(id) ON DELETE CASCADE,
                    FOREIGN KEY (criado_por) REFERENCES usuarios(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            conn.commit()
            logger.info("[CORREÇÃO] Tabela registros_ponto criada com sucesso!")
        else:
            # Verificar se a coluna status_pontualidade existe
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = 'controle_ponto' 
                AND table_name = 'registros_ponto' 
                AND column_name = 'status_pontualidade'
            """)
            
            result = cursor.fetchone()
            coluna_existe = result['COUNT(*)'] > 0
            
            if not coluna_existe:
                logger.warning("[CORREÇÃO] Adicionando coluna status_pontualidade à tabela registros_ponto...")
                
                # Adicionar a coluna status_pontualidade
                cursor.execute("""
                    ALTER TABLE registros_ponto
                    ADD COLUMN status_pontualidade ENUM('Pontual', 'Atrasado') NULL
                    COMMENT 'Status de pontualidade do registro'
                """)
                
                # Adicionar índice para a nova coluna
                cursor.execute("""
                    ALTER TABLE registros_ponto
                    ADD INDEX idx_status_pontualidade (status_pontualidade)
                """)
                
                conn.commit()
                logger.info("[CORREÇÃO] Coluna status_pontualidade adicionada com sucesso!")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"[CORREÇÃO] Erro ao verificar/criar tabela registros_ponto: {str(e)}")
        return False

def obter_hora_atual(fuso_horario_manaus=True):
    """
    Obtém a hora atual do sistema, com preferência por NTP quando disponível.
    Se solicitado, ajusta para o fuso horário de Manaus/AM (GMT-4).

    Returns:
        datetime: Data e hora atual, ajustada para fuso de Manaus se solicitado
    """
    hora_atual = None
    
    # Tentar obter hora via NTP
    try:
        client = ntplib.NTPClient()
        response = client.request('pool.ntp.org', timeout=1)
        hora_atual = datetime.fromtimestamp(response.tx_time)
        logger.info("Hora obtida via NTP")
    except Exception as e:
        logger.warning(f"Erro ao obter hora via NTP: {str(e)}. Usando hora local.")
        hora_atual = datetime.now()
    
    # Ajustar para fuso de Manaus (GMT-4) se solicitado
    if fuso_horario_manaus:
        # A implementação ideal usaria pytz ou datetime.astimezone()
        # Esta é uma aproximação simplificada para o fuso de Manaus (-4h)
        # Em produção com Python 3.9+, use zoneinfo ou pytz
        
        # Verificamos se a hora já está no fuso correto
        # Se a diferença for maior que 4 horas, ajustamos
        # Essa lógica assume que o servidor está em GMT
        hora_utc = datetime.utcnow()
        diferenca = (hora_atual - hora_utc).total_seconds() / 3600
        
        # Se não estiver próximo do fuso de Manaus (-4h)
        if abs(diferenca + 4) > 1:
            logger.info(f"Ajustando fuso: diferença atual é {diferenca}h do UTC")
            # Ajustar para GMT-4 (Manaus)
            hora_atual = hora_utc.replace(tzinfo=None) - datetime.timedelta(hours=4)
    
    return hora_atual

def validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario):
    """
    Valida se o tipo de registro solicitado é permitido no horário atual.
    
    Args:
        tipo_registro (str): Tipo de registro (entrada_manha, saida_almoco, etc.)
        horarios_funcionario (dict): Horários configurados do funcionário
    
    Returns:
        dict: {'permitido': bool, 'mensagem': str, 'horario_liberado': bool, 'status': str}
    """
    # Obter hora atual no fuso de Manaus
    hora_atual = obter_hora_atual(fuso_horario_manaus=True)
    hora_atual_str = hora_atual.strftime('%H:%M')
    
    # Converter horários de string para objetos time para comparação
    def string_para_time(hora_str):
        if not hora_str:
            return None
        try:
            horas, minutos = map(int, hora_str.split(':'))
            return time(horas, minutos)
        except (ValueError, AttributeError):
            return None
    
    entrada_manha = string_para_time(horarios_funcionario['entrada_manha'])
    saida_almoco = string_para_time(horarios_funcionario['saida_almoco'])
    entrada_tarde = string_para_time(horarios_funcionario['entrada_tarde'])
    saida = string_para_time(horarios_funcionario['saida'])
    
    # Tolerância em minutos
    tolerancia = horarios_funcionario['tolerancia_minutos']
    
    # Converter hora atual para objeto time
    hora_atual_obj = hora_atual.time()
    
    # Determinar o período do dia em que estamos
    # Isso nos ajuda a decidir quais tipos de registro são apropriados
    periodo_atual = None
    
    if entrada_manha and saida_almoco:
        # Período da manhã (entre entrada_manha e saida_almoco)
        if entrada_manha <= hora_atual_obj <= saida_almoco:
            periodo_atual = "manha"
    
    if saida_almoco and entrada_tarde:
        # Período de almoço (entre saida_almoco e entrada_tarde)
        if saida_almoco <= hora_atual_obj <= entrada_tarde:
            periodo_atual = "almoco"
    
    if entrada_tarde and saida:
        # Período da tarde (entre entrada_tarde e saida)
        if entrada_tarde <= hora_atual_obj <= saida:
            periodo_atual = "tarde"
    
    # Se estamos depois do horário de saída
    if saida and hora_atual_obj > saida:
        periodo_atual = "apos_expediente"
    
    # Se estamos antes do horário de entrada
    if entrada_manha and hora_atual_obj < entrada_manha:
        periodo_atual = "antes_expediente"
    
    logger.info(f"[VALIDAÇÃO] Hora atual: {hora_atual_str}, Período determinado: {periodo_atual}")
    
    # Inicializar status padrão como "pontual"
    status_registro = "Pontual"
    
    # Validar cada tipo de registro com base no período atual
    if tipo_registro == 'entrada_manha':
        # Entrada manhã é permitida a partir do horário de entrada menos tolerância
        if entrada_manha is None:
            return {'permitido': False, 'mensagem': 'Horário de entrada não configurado', 'horario_liberado': False, 'status': None}
        
        # ✅ CORREÇÃO: Verificar se o registro será considerado "Atrasado" ou "Pontual"
        # Horário limite de tolerância para ser considerado pontual
        hora_limite_pontualidade = datetime.combine(date.today(), entrada_manha)
        hora_limite_pontualidade = (hora_limite_pontualidade + timedelta(minutes=tolerancia)).time()
        
        # Se estiver após o limite de tolerância, marcar como "Atrasado"
        if hora_atual_obj > hora_limite_pontualidade:
            status_registro = "Atrasado"
        else:
            status_registro = "Pontual"
        
        # ✅ CORREÇÃO: Não permitir registro antes do horário programado
        # Permitir registro apenas a partir do horário programado (sem tolerância antes)
        hora_min = datetime.combine(date.today(), entrada_manha)
        hora_min = hora_min.time()  # Não aplicar tolerância negativa
        
        # ✅ CORREÇÃO: Definir um limite máximo para registro de entrada
        # Permitir registro apenas até X minutos após o horário programado
        limite_atraso = 60  # 1 hora de limite para registrar entrada atrasada
        hora_max = datetime.combine(date.today(), entrada_manha)
        hora_max = (hora_max + timedelta(minutes=limite_atraso)).time()
        
        # Entrada manhã é mais apropriada no início do dia
        if periodo_atual in ["antes_expediente", "manha"]:
            # ✅ CORREÇÃO: Se for antes do horário programado, não permitir o registro
            if hora_atual_obj < hora_min:
                return {
                    'permitido': False, 
                    'mensagem': f"Entrada ainda não liberada. Disponível a partir de {hora_min.strftime('%H:%M')}", 
                    'horario_liberado': False,
                    'status': None
                }
            # ✅ CORREÇÃO: Verificar se passou do limite máximo permitido
            elif hora_atual_obj > hora_max:
                return {
                    'permitido': False,
                    'mensagem': f"Horário de entrada expirado. O limite para registro era {hora_max.strftime('%H:%M')}",
                    'horario_liberado': False,
                    'status': None
                }
            return {
                'permitido': True, 
                'mensagem': f"Entrada registrada com sucesso ({status_registro})", 
                'horario_liberado': True,
                'status': status_registro
            }
        else:
            # ✅ CORREÇÃO: Não permitir registro fora do período adequado
            return {
                'permitido': False,
                'mensagem': f"Não é possível registrar entrada da manhã no período atual ({periodo_atual})",
                'horario_liberado': False,
                'status': None
            }
    
    elif tipo_registro == 'saida_almoco':
        # Saída almoço é permitida a partir do horário configurado
        if saida_almoco is None:
            return {'permitido': False, 'mensagem': 'Horário de saída para almoço não configurado', 'horario_liberado': False, 'status': None}
        
        # ✅ CORREÇÃO CRÍTICA: Definir limite máximo rigoroso para registro de saída de almoço
        hora_min = datetime.combine(date.today(), saida_almoco)
        hora_min = hora_min.time()  # Horário mínimo (sem tolerância negativa)
        
        # ✅ CORREÇÃO CRÍTICA: Limite máximo muito restrito - apenas 30 minutos após o horário
        limite_maximo = 30  # Apenas 30 minutos para registrar saída de almoço
        hora_max = datetime.combine(date.today(), saida_almoco)
        hora_max = (hora_max + timedelta(minutes=limite_maximo)).time()
        
        # ✅ CORREÇÃO: Verificar se hora atual está após o horário mínimo
        if hora_atual_obj < hora_min:
            # Antes do horário permitido - não liberar ainda
            return {
                'permitido': False, 
                'mensagem': f"Saída para almoço ainda não liberada. Disponível a partir de {hora_min.strftime('%H:%M')}", 
                'horario_liberado': False,
                'status': None
            }
        # ✅ CORREÇÃO CRÍTICA: Se passou do limite máximo, NÃO permitir mais o registro
        elif hora_atual_obj > hora_max:
            return {
                'permitido': False,
                'mensagem': f"Horário de saída para almoço expirado. O limite era {hora_max.strftime('%H:%M')}",
                'horario_liberado': False,
                'status': None
            }
        else:
            # Dentro do período permitido
            # Verificar status (pontual ou atrasado)
            hora_limite_pontualidade = datetime.combine(date.today(), saida_almoco)
            hora_limite_pontualidade = (hora_limite_pontualidade + timedelta(minutes=tolerancia)).time()
            
            if hora_atual_obj > hora_limite_pontualidade:
                status_registro = "Atrasado"
            else:
                status_registro = "Pontual"
                
            return {
                'permitido': True, 
                'mensagem': f"Saída para almoço registrada com sucesso ({status_registro})", 
                'horario_liberado': True,
                'status': status_registro
            }
    
    elif tipo_registro == 'entrada_tarde':
        # Entrada tarde (retorno almoço) é permitida a partir do horário configurado
        if entrada_tarde is None:
            return {'permitido': False, 'mensagem': 'Horário de retorno de almoço não configurado', 'horario_liberado': False, 'status': None}
        
        # ✅ CORREÇÃO CRÍTICA: Definir limite máximo rigoroso para registro de retorno de almoço
        hora_min = datetime.combine(date.today(), entrada_tarde)
        hora_min = hora_min.time()  # Horário mínimo (sem tolerância negativa)
        
        # ✅ CORREÇÃO CRÍTICA: Limite máximo muito restrito - apenas 60 minutos após o horário
        limite_maximo = 60  # Apenas 1 hora para registrar retorno de almoço
        hora_max = datetime.combine(date.today(), entrada_tarde)
        hora_max = (hora_max + timedelta(minutes=limite_maximo)).time()
        
        # ✅ CORREÇÃO: Verificar se hora atual está após o horário mínimo
        if hora_atual_obj < hora_min:
            # Antes do horário permitido - não liberar ainda
            return {
                'permitido': False, 
                'mensagem': f"Retorno de almoço ainda não liberado. Disponível a partir de {hora_min.strftime('%H:%M')}", 
                'horario_liberado': False,
                'status': None
            }
        # ✅ CORREÇÃO CRÍTICA: Se passou do limite máximo, NÃO permitir mais o registro
        elif hora_atual_obj > hora_max:
            return {
                'permitido': False,
                'mensagem': f"Horário de retorno de almoço expirado. O limite era {hora_max.strftime('%H:%M')}",
                'horario_liberado': False,
                'status': None
            }
        else:
            # Dentro do período permitido
            # Verificar status (pontual ou atrasado)
            hora_limite_pontualidade = datetime.combine(date.today(), entrada_tarde)
            hora_limite_pontualidade = (hora_limite_pontualidade + timedelta(minutes=tolerancia)).time()
            
            if hora_atual_obj > hora_limite_pontualidade:
                status_registro = "Atrasado"
            else:
                status_registro = "Pontual"
                
            return {
                'permitido': True, 
                'mensagem': f"Retorno de almoço registrado com sucesso ({status_registro})", 
                'horario_liberado': True,
                'status': status_registro
            }
        
    elif tipo_registro == 'saida':
        # Saída é permitida a partir do horário configurado
        if saida is None:
            return {'permitido': False, 'mensagem': 'Horário de saída não configurado', 'horario_liberado': False, 'status': None}
        
        # ✅ CORREÇÃO: Verificar status (sempre pontual para saída, pois saída antes é considerada pontual)
        status_registro = "Pontual"
        
        # ✅ CORREÇÃO: Saída só deve ser permitida a partir do horário programado (ou tolerância antes)
        hora_min = datetime.combine(date.today(), saida)
        hora_min = (hora_min - timedelta(minutes=tolerancia)).time()
        
        # ✅ CORREÇÃO: Definir um limite máximo para registro de saída
        limite_atraso = 120  # 2 horas de limite para registrar saída (aumentado para melhor flexibilidade)
        hora_max = datetime.combine(date.today(), saida)
        hora_max = (hora_max + timedelta(minutes=limite_atraso)).time()
        
        # ✅ CORREÇÃO: Verificar se hora atual está após o início da janela de tolerância
        if hora_atual_obj >= hora_min:
            # Hora atual está após o horário mínimo permitido para saída
            # ✅ CORREÇÃO: Verificar se passou do limite máximo permitido
            if hora_atual_obj > hora_max:
                return {
                    'permitido': False,
                    'mensagem': f"Horário de saída expirado. O limite para registro era {hora_max.strftime('%H:%M')}",
                    'horario_liberado': False,
                    'status': None
                }
            return {
                'permitido': True, 
                'mensagem': f"Saída registrada com sucesso ({status_registro})", 
                'horario_liberado': True,
                'status': status_registro
            }
        else:
            # Antes do horário permitido - não liberar ainda
            return {
                'permitido': False, 
                'mensagem': f"Saída ainda não liberada. Disponível a partir de {hora_min.strftime('%H:%M')}", 
                'horario_liberado': False,
                'status': None
            }
    
    else:
        return {'permitido': False, 'mensagem': f'Tipo de registro não reconhecido: {tipo_registro}', 'horario_liberado': False, 'status': None}

def verificar_registros_ausentes(funcionario_id):
    """
    Verifica horários que não foram registrados e que já passaram do limite de tempo.
    Identifica ausências nos registros de ponto do funcionário.
    
    Args:
        funcionario_id (int): ID do funcionário
        
    Returns:
        list: Lista de tipos de registro ausentes
    """
    try:
        # Obter data atual
        data_atual = date.today()
        data_inicio = data_atual.strftime('%Y-%m-%d 00:00:00')
        data_fim = data_atual.strftime('%Y-%m-%d 23:59:59')
        
        # ✅ MELHORIA: Verificar a data de cadastro do funcionário
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        cursor.execute("""
            SELECT 
                DATE(data_cadastro) AS data_cadastro, 
                nome_completo
            FROM funcionarios
            WHERE id = %s AND ativo = TRUE
        """, (funcionario_id,))
        
        info_funcionario = cursor.fetchone()
        
        if not info_funcionario:
            logger.warning(f"Funcionário {funcionario_id} não encontrado ou inativo ao verificar ausências")
            conn.close()
            return []
        
        # ✅ MELHORIA: Não verificar ausências no dia do cadastro
        data_cadastro = info_funcionario['data_cadastro'].date() if hasattr(info_funcionario['data_cadastro'], 'date') else info_funcionario['data_cadastro']
        
        if data_cadastro == data_atual:
            logger.info(f"Funcionário {info_funcionario['nome_completo']} cadastrado hoje. Ignorando verificação de ausências.")
            conn.close()
            return []
        
        # Obter horários do funcionário
        horarios = obter_horarios_funcionario(funcionario_id)
        
        # Obter hora atual
        hora_atual = obter_hora_atual(fuso_horario_manaus=True)
        hora_atual_obj = hora_atual.time()
        
        # Obter registros do dia
        cursor.execute("""
            SELECT tipo_registro, data_hora
            FROM registros_ponto 
            WHERE funcionario_id = %s 
            AND data_hora BETWEEN %s AND %s
        """, (funcionario_id, data_inicio, data_fim))
        
        registros = cursor.fetchall()
        
        # Converter registros para conjunto de tipos para facilitar a verificação
        tipos_registrados = {reg['tipo_registro'] for reg in registros}
        
        # Lista de ausências
        ausencias = []
        
        # Verificar cada tipo de registro
        def string_para_time(hora_str):
            if not hora_str:
                return None
            try:
                horas, minutos = map(int, hora_str.split(':'))
                return time(horas, minutos)
            except (ValueError, AttributeError):
                return None
        
        # Definir a sequência de tipos na ordem correta
        sequencia = ['entrada_manha', 'saida_almoco', 'entrada_tarde', 'saida']
        
        # ✅ CORREÇÃO: Definir limites de tempo para cada tipo de registro
        limites_atraso = {
            'entrada_manha': 60,   # 1 hora após o horário programado
            'saida_almoco': 60,    # 1 hora após o horário programado
            'entrada_tarde': 120,  # 2 horas após o horário programado
            'saida': 15            # 15 minutos após o horário programado
        }
        
        # Para cada tipo na sequência, verificar se:
        # 1. O horário está configurado
        # 2. O horário já passou (considerando a tolerância)
        # 3. Não foi registrado
        for tipo in sequencia:
            # Obter o horário configurado para este tipo
            horario_str = None
            if tipo == 'entrada_manha':
                horario_str = horarios.get('entrada_manha')
            elif tipo == 'saida_almoco':
                horario_str = horarios.get('saida_almoco')
            elif tipo == 'entrada_tarde':
                horario_str = horarios.get('entrada_tarde')
            elif tipo == 'saida':
                horario_str = horarios.get('saida')
                
            if not horario_str:
                continue  # Se não há horário configurado, pular
            
            horario_time = string_para_time(horario_str)
            if not horario_time:
                continue  # Se horário inválido, pular
            
            # ✅ CORREÇÃO: Usar o limite de tempo específico para cada tipo de registro
            limite_atraso = limites_atraso.get(tipo, 30)  # Padrão de 30 minutos se não especificado
            
            # Adicionar limite de atraso ao horário programado
            horario_limite = datetime.combine(data_atual, horario_time) + timedelta(minutes=limite_atraso)
            
            # ✅ MELHORIA: Se já passou do horário limite e não foi registrado, 
            # marcar como ausente independentemente dos outros registros
            if hora_atual > horario_limite and tipo not in tipos_registrados:
                ausencias.append({
                    'tipo': tipo,
                    'horario_previsto': horario_str,
                    'status': 'AUSENTE',
                    'limite_registro': horario_limite.strftime('%H:%M')
                })
        
        conn.close()
        
        if len(ausencias) > 0:
            logger.info(f"[AUSÊNCIAS] Detectadas {len(ausencias)} ausências para funcionário {funcionario_id}: {ausencias}")
        
        return ausencias
        
    except Exception as e:
        logger.error(f"Erro ao verificar registros ausentes: {str(e)}")
        return []

# ========================================
# ROTAS PRINCIPAIS
# ========================================

@registro_ponto_bp.route('/biometrico')
@require_login
def pagina_registro_biometrico():
    """
    Página de registro de ponto biométrico.
    Interface para captura biométrica via ZK4500.
    """
    try:
        context = {
            'titulo': 'Registro de Ponto Biométrico',
            'usuario_logado': session.get('usuario', 'Usuário'),
            'data_atual': date.today().strftime('%d/%m/%Y'),
            'hora_atual': datetime.now().strftime('%H:%M')
        }
        
        return render_template('registro_ponto/biometrico.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao carregar página biométrica: {str(e)}")
        # Corrigir redirecionamento para função correta
        return redirect(url_for('funcionarios.index'))

# Duplicar para o blueprint com underscore
@registro_ponto_underscore_bp.route('/biometrico')
@require_login
def pagina_registro_biometrico_underscore():
    """Versão duplicada da rota biometrico com underscore"""
    return pagina_registro_biometrico()

@registro_ponto_bp.route('/manual')
@require_login
def pagina_registro_manual():
    """
    Página de registro de ponto manual.
    Lista funcionários para seleção e registro manual.
    """
    try:
        logger.info(f"[DEBUG] Iniciando pagina_registro_manual para usuário: {session.get('usuario', 'desconhecido')}")
        
        # Verificar e criar tabela se necessário
        if not verificar_e_criar_tabela_registros():
            logger.error("[DEBUG] Falha ao verificar/criar tabela registros_ponto")
            
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        logger.info(f"[DEBUG] Nível de acesso: {nivel_acesso}")
        
        # Obter lista de funcionários ativos
        logger.info("[DEBUG] Iniciando conexão com banco de dados")
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        logger.info("[DEBUG] Conexão com banco estabelecida")
        
        logger.info("[DEBUG] Executando query para buscar funcionários")
        cursor.execute("""
            SELECT 
                f.id,
                f.nome_completo,
                f.cpf,
                f.matricula_empresa,
                f.cargo,
                CASE 
                    WHEN f.setor_obra IS NULL OR f.setor_obra = '' THEN 
                        CASE 
                            WHEN f.setor IS NULL OR f.setor = '' THEN 'Não informado'
                            ELSE f.setor
                        END
                    ELSE f.setor_obra
                END as setor,
                f.foto_3x4,
                COALESCE(e.nome_fantasia, 'Empresa Padrão') AS empresa,
                COALESCE(ht.nome_horario, 'Horário Padrão') as nome_horario
            FROM funcionarios f
            LEFT JOIN empresas e ON f.empresa_id = e.id
            LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id
            WHERE f.ativo = TRUE
            ORDER BY f.nome_completo
        """)
        
        funcionarios_raw = cursor.fetchall()
        logger.info(f"[DEBUG] Query executada com sucesso. {len(funcionarios_raw)} funcionários encontrados")
        conn.close()
        logger.info("[DEBUG] Conexão com banco fechada")
        
        # Processar lista de funcionários
        logger.info("[DEBUG] Iniciando processamento da lista de funcionários")
        funcionarios = []
        for i, f in enumerate(funcionarios_raw):
            try:
                # Validar se o CPF existe antes de tentar mascarar
                cpf_original = f['cpf'] if f['cpf'] else ''
                cpf_exibicao = ''
                
                if cpf_original:
                    try:
                        from utils.helpers import mascarar_cpf
                        cpf_exibicao = mascarar_cpf(cpf_original) if nivel_acesso != 'admin' else cpf_original
                    except Exception as e:
                        logger.warning(f"[DEBUG] Erro ao mascarar CPF para funcionário {f['nome_completo']}: {str(e)}")
                        cpf_exibicao = '***.***.***-**'
                else:
                    cpf_exibicao = 'CPF não informado'
                
                # O setor já está tratado na consulta SQL com CASE WHEN
                funcionario = {
                    'id': f['id'],
                    'nome_completo': f['nome_completo'] or 'Nome não informado',
                    'cpf': cpf_original,
                    'cpf_exibicao': cpf_exibicao,
                    'matricula_empresa': f['matricula_empresa'] or 'Sem matrícula',
                    'cargo': f['cargo'] or 'Não informado',
                    'setor': f['setor'],  # Já está tratado na consulta SQL
                    'foto_url': normalizar_caminho_foto(f['foto_3x4']),
                    'empresa': f['empresa'],
                    'horario_trabalho': f['nome_horario']
                }
                funcionarios.append(funcionario)
                if i < 3:  # Log apenas os primeiros 3 para não sobrecarregar
                    logger.info(f"[DEBUG] Funcionário {i+1} processado: {funcionario['nome_completo']}")
            except Exception as e:
                logger.error(f"[DEBUG] Erro ao processar funcionário {i+1}: {str(e)}")
                continue
                
        logger.info(f"[DEBUG] Lista de funcionários processada. Total: {len(funcionarios)}")
        
        logger.info("[DEBUG] Montando contexto do template")
        context = {
            'titulo': 'Registro de Ponto Manual',
            'funcionarios': funcionarios,
            'total_funcionarios': len(funcionarios),
            'data_atual': date.today().strftime('%d/%m/%Y'),
            'hora_atual': datetime.now().strftime('%H:%M'),
            'nivel_acesso': nivel_acesso
        }
        logger.info(f"[DEBUG] Contexto montado com {len(context)} variáveis")
        
        logger.info("[DEBUG] Tentando renderizar template registro_ponto/manual.html")
        resultado = render_template('registro_ponto/manual.html', **context)
        logger.info("[DEBUG] Template renderizado com sucesso")
        
        return resultado
        
    except Exception as e:
        error_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        logger.error(f"[ERROR] {error_id} - Erro ao carregar página manual: {str(e)}")
        logger.error(f"[ERROR] {error_id} - Traceback: ", exc_info=True)
        
        # Retornar página de erro personalizada em vez de redirect
        return render_template('erro.html', 
                             codigo=500,
                             titulo="Erro interno do sistema",
                             mensagem=f"Ocorreu um erro inesperado. ID: {error_id}",
                             error_id=error_id), 500

# Duplicar para o blueprint com underscore
@registro_ponto_underscore_bp.route('/manual')
@require_login
def pagina_registro_manual_underscore():
    """Versão duplicada da rota manual com underscore"""
    return pagina_registro_manual()

# ========================================
# APIs DE REGISTRO
# ========================================

@registro_ponto_bp.route('/api/registrar-biometrico', methods=['POST'])
@require_login
def api_registrar_biometrico():
    """
    API para processar registro de ponto biométrico.
    Valida dados, horários e registra ponto biométrico no banco.
    """
    try:
        dados = request.get_json()
        
        # Validar dados obrigatórios
        funcionario_id = dados.get('funcionario_id')
        tipo_registro = dados.get('tipo_registro')
        qualidade_biometria = dados.get('qualidade_biometria')
        observacoes = dados.get('observacoes', '')
        
        if not all([funcionario_id, tipo_registro]):
            return jsonify({
                'success': False,
                'message': 'Dados obrigatórios não informados'
            }), 400
        
        # Validar se funcionário existe e está ativo
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        cursor.execute("""
            SELECT id, nome_completo, ativo 
            FROM funcionarios 
            WHERE id = %s
        """, (funcionario_id,))
        
        funcionario = cursor.fetchone()
        conn.close()
        
        if not funcionario:
            return jsonify({
                'success': False,
                'message': 'Funcionário não encontrado'
            }), 404
        
        if not funcionario['ativo']:
            return jsonify({
                'success': False,
                'message': 'Funcionário inativo'
            }), 400
        
        # Validar duplicata
        data_atual = date.today()
        duplicata = validar_duplicata_registro(funcionario_id, tipo_registro, data_atual)
        
        if duplicata['existe']:
            return jsonify({
                'success': False,
                'message': duplicata['mensagem'],
                'registro_existente': duplicata['registro_existente']
            }), 400
        
        # ✅ VALIDAÇÃO - Verificar se tipo de registro é permitido no horário atual
        horarios_funcionario = obter_horarios_funcionario(funcionario_id)
        validacao_horario = validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario)
        
        if not validacao_horario['permitido']:
            return jsonify({
                'success': False,
                'message': validacao_horario['mensagem']
            }), 400
        
        # ✅ MELHORIA: Usar diretamente as observações informadas pelo usuário, sem adicionar o horário
        # Registrar ponto biométrico
        resultado = registrar_ponto_no_banco(
            funcionario_id=funcionario_id,
            tipo_registro=tipo_registro,
            metodo_registro='biometrico',
            observacoes=observacoes,  # Observações exatamente como fornecidas pelo usuário
            qualidade_biometria=qualidade_biometria
        )
        
        if resultado['success']:
            return jsonify({
                'success': True,
                'message': resultado['message'],
                'registro_id': resultado['registro_id'],
                'funcionario_nome': funcionario['nome_completo'],
                'data_hora': datetime.now().strftime('%d/%m/%Y %H:%M')
            })
        else:
            return jsonify({
                'success': False,
                'message': resultado['message']
            }), 500
        
    except Exception as e:
        logger.error(f"Erro no registro biométrico: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/registrar-biometrico', methods=['POST'])
@require_login
def api_registrar_biometrico_underscore():
    """Versão duplicada da API com underscore"""
    return api_registrar_biometrico()

@registro_ponto_bp.route('/api/registrar-manual', methods=['POST'])
@require_login
def api_registrar_manual():
    """
    API para registrar ponto manual.
    """
    try:
        # Obter dados do formulário
        funcionario_id = request.form.get('funcionario_id')
        tipo_registro = request.form.get('tipo_registro')
        observacoes = request.form.get('observacoes', '')
        
        # Validar dados
        if not funcionario_id or not tipo_registro:
            return jsonify({'success': False, 'message': 'Dados incompletos'}), 400
        
        funcionario_id = int(funcionario_id)
        
        # Obter horários do funcionário
        horarios_funcionario = obter_horarios_funcionario(funcionario_id)
        
        # Verificar duplicata antes de validar o horário para melhorar feedback
        data_atual = date.today()
        resultado_duplicata = validar_duplicata_registro(funcionario_id, tipo_registro, data_atual)
        if resultado_duplicata['existe']:
            return jsonify({
                'success': False, 
                'message': resultado_duplicata['mensagem'],
                'registro_existente': resultado_duplicata['registro_existente'],
                'code': 'DUPLICATE_RECORD'  # Código para auxiliar o front-end
            }), 400
        
        # Validar tipo de registro pelo horário
        validacao = validar_tipo_registro_por_horario(tipo_registro, horarios_funcionario)
        if not validacao['permitido']:
            return jsonify({
                'success': False, 
                'message': validacao['mensagem'],
                'code': 'NOT_ALLOWED_TIME'  # Código para auxiliar o front-end
            }), 400
        
        # ✅ CORREÇÃO: Obter o status de pontualidade (Pontual/Atrasado)
        status_pontualidade = validacao.get('status')
        
        # ✅ CORREÇÃO: Obter hora atual para usar na resposta
        hora_atual = obter_hora_atual(fuso_horario_manaus=True)
        
        # Registrar ponto com o status de pontualidade
        resultado = registrar_ponto_no_banco(
            funcionario_id=funcionario_id,
            tipo_registro=tipo_registro,
            metodo_registro='manual',
            observacoes=observacoes,
            status_pontualidade=status_pontualidade  # ✅ CORREÇÃO: Passar o status de pontualidade
        )
        
        if not resultado['success']:
            return jsonify(resultado), 500
        
        # ✅ MELHORIA: Calcular e atualizar horas trabalhadas, horas extras e banco de horas
        try:
            conn = get_db_connection()
            cursor = conn.cursor(DictCursor)
            
            # Obter todos os registros do dia para o funcionário
            data_inicio = data_atual.strftime('%Y-%m-%d 00:00:00')
            data_fim = data_atual.strftime('%Y-%m-%d 23:59:59')
            
            cursor.execute("""
                SELECT id, funcionario_id, tipo_registro, data_hora, metodo_registro
                FROM registros_ponto
                WHERE funcionario_id = %s AND data_hora BETWEEN %s AND %s
                ORDER BY data_hora ASC
            """, (funcionario_id, data_inicio, data_fim))
            
            registros_dia = cursor.fetchall()
            
            # Obter informações do funcionário para cálculo de horas extras e banco de horas
            cursor.execute("""
                SELECT 
                    id, nome_completo, 
                    jornada_seg_qui_entrada, jornada_seg_qui_saida,
                    jornada_intervalo_entrada, jornada_intervalo_saida,
                    banco_horas, hora_extra
                FROM funcionarios
                WHERE id = %s
            """, (funcionario_id,))
            
            funcionario_info = cursor.fetchone()
            
            # Calcular horas trabalhadas, extras e banco de horas
            resultado_horas = calcular_horas_trabalhadas(registros_dia, funcionario_info)
            
            # Se o dia está completo, atualizar horas extras e banco de horas
            if resultado_horas['completo']:
                # Atualizar tabela de registros para o dia atual
                cursor.execute("""
                    UPDATE registros_ponto 
                    SET 
                        horas_trabalhadas = %s,
                        horas_extras = %s,
                        banco_horas = %s
                    WHERE funcionario_id = %s 
                    AND data_hora BETWEEN %s AND %s
                """, (
                    resultado_horas['horas_trabalhadas'],
                    resultado_horas['horas_extras'],
                    resultado_horas['banco_horas'],
                    funcionario_id,
                    data_inicio,
                    data_fim
                ))
                
                # Atualizar acumulado de banco de horas e horas extras na tabela de funcionários
                if resultado_horas['horas_extras'] > 0 or resultado_horas['banco_horas'] > 0:
                    cursor.execute("""
                        UPDATE funcionarios
                        SET 
                            acumulado_horas_extras = IFNULL(acumulado_horas_extras, 0) + %s,
                            acumulado_banco_horas = IFNULL(acumulado_banco_horas, 0) + %s
                        WHERE id = %s
                    """, (
                        resultado_horas['horas_extras'],
                        resultado_horas['banco_horas'],
                        funcionario_id
                    ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Erro ao calcular horas trabalhadas: {str(e)}")
            # Não falhar o registro principal se o cálculo de horas falhar
        
        # ✅ CORREÇÃO: Incluir mais detalhes na resposta
        # Obter nome do funcionário para a resposta
        try:
            conn = get_db_connection()
            cursor = conn.cursor(DictCursor)
            
            cursor.execute("SELECT nome_completo FROM funcionarios WHERE id = %s", (funcionario_id,))
            funcionario = cursor.fetchone()
            nome_funcionario = funcionario['nome_completo'] if funcionario else 'Funcionário'
            
            conn.close()
        except Exception as e:
            logger.error(f"Erro ao buscar nome do funcionário: {str(e)}")
            nome_funcionario = 'Funcionário'
        
        # ✅ CORREÇÃO: Incluir o status de pontualidade e dados detalhados na resposta
        return jsonify({
            'success': True,
            'message': f'Ponto registrado com sucesso ({status_pontualidade})',
            'registro_id': resultado['registro_id'],
            'status_pontualidade': status_pontualidade,  # Incluir na resposta
            'hora': hora_atual.strftime('%H:%M:%S'),
            'data_hora': hora_atual.strftime('%Y-%m-%d %H:%M:%S'),
            'funcionario': {
                'id': funcionario_id,
                'nome': nome_funcionario
            },
            'tipo_registro': tipo_registro,
            'tipo_registro_formatado': tipo_registro.replace('_', ' ').title(),
            'horas_calculadas': resultado_horas if 'resultado_horas' in locals() else None
        })
        
    except Exception as e:
        logger.error(f"Erro ao registrar ponto manual: {str(e)}")
        return jsonify({'success': False, 'message': f'Erro ao registrar ponto: {str(e)}'}), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/registrar-manual', methods=['POST'])
@require_login
def api_registrar_manual_underscore():
    """Versão duplicada da API com underscore"""
    return api_registrar_manual()

@registro_ponto_bp.route('/api/obter-horarios/<int:funcionario_id>')
@require_login
def api_obter_horarios(funcionario_id):
    """
    API para obter horários de trabalho de um funcionário.
    Usado no modal de registro manual.
    Filtra tipos de registro disponíveis com base no horário atual.
    """
    try:
        horarios = obter_horarios_funcionario(funcionario_id)
        
        # Converter times para strings
        horarios_formatados = {
            'entrada_manha': horarios['entrada_manha'],
            'saida_almoco': horarios['saida_almoco'],
            'entrada_tarde': horarios['entrada_tarde'],
            'saida': horarios['saida'],
            'tolerancia_minutos': horarios['tolerancia_minutos'],
            'nome_horario': horarios['nome_horario']
        }
        
        # ✅ MELHORIA: Adicionar informações de tolerância explícitas
        horarios_com_tolerancia = {}
        tolerancia = horarios['tolerancia_minutos']
        
        # Calcular horários com tolerância
        for tipo, horario_str in {
            'entrada_manha': horarios['entrada_manha'],
            'saida_almoco': horarios['saida_almoco'],
            'entrada_tarde': horarios['entrada_tarde'],
            'saida': horarios['saida']
        }.items():
            if not horario_str:
                continue
                
            try:
                # Converter string para objeto time
                horas, minutos = map(int, horario_str.split(':'))
                horario_time = time(horas, minutos)
                
                # Para entradas: calcular horário + tolerância
                if tipo in ['entrada_manha', 'entrada_tarde']:
                    horario_com_tolerancia = datetime.combine(date.today(), horario_time) + timedelta(minutes=tolerancia)
                    horario_tolerancia_str = horario_com_tolerancia.strftime('%H:%M')
                    horarios_com_tolerancia[tipo] = {
                        'horario': horario_str,
                        'com_tolerancia': horario_tolerancia_str,
                        'mensagem': f"Permitido até {horario_tolerancia_str}"
                    }
                # ✅ CORREÇÃO: Lógica específica para saida_almoco (tolerância após o horário padrão)
                elif tipo == 'saida_almoco':
                    # Para saída almoço, adicionar tolerância (pode sair até X min depois)
                    horario_com_tolerancia = datetime.combine(date.today(), horario_time) + timedelta(minutes=tolerancia)
                    horario_tolerancia_str = horario_com_tolerancia.strftime('%H:%M')
                    horarios_com_tolerancia[tipo] = {
                        'horario': horario_str,
                        'com_tolerancia': horario_tolerancia_str,
                        'mensagem': f"Permitido até {horario_tolerancia_str}"
                    }
                # Para outras saídas: calcular horário - tolerância
                else:
                    horario_com_tolerancia = datetime.combine(date.today(), horario_time) - timedelta(minutes=tolerancia)
                    horario_tolerancia_str = horario_com_tolerancia.strftime('%H:%M')
                    horarios_com_tolerancia[tipo] = {
                        'horario': horario_str,
                        'com_tolerancia': horario_tolerancia_str,
                        'mensagem': f"Permitido a partir de {horario_tolerancia_str}"
                    }
            except (ValueError, TypeError, AttributeError) as e:
                logger.warning(f"Erro ao calcular tolerância para {tipo}: {e}")
        
        # Gerar opções de tipos de registro baseadas nos horários
        todos_tipos = []
        tipos_liberados = []
        
        # Hora atual para mostrar no frontend
        hora_atual = obter_hora_atual().strftime('%H:%M')
        
        if horarios['entrada_manha']:
            tipo = {
                'value': 'entrada_manha',
                'text': f'Entrada Manhã ({horarios_formatados["entrada_manha"]})',
                'tolerancia': horarios_com_tolerancia.get('entrada_manha', {}).get('mensagem', '')
            }
            todos_tipos.append(tipo)
            
            # ✅ VALIDAR SE ESTE TIPO ESTÁ LIBERADO NO HORÁRIO ATUAL
            validacao = validar_tipo_registro_por_horario('entrada_manha', horarios)
            if validacao['horario_liberado']:
                tipos_liberados.append(tipo)
        
        if horarios['saida_almoco']:
            tipo = {
                'value': 'saida_almoco',
                'text': f'Saída Almoço ({horarios_formatados["saida_almoco"]})',
                'tolerancia': horarios_com_tolerancia.get('saida_almoco', {}).get('mensagem', '')
            }
            todos_tipos.append(tipo)
            
            # ✅ VALIDAR SE ESTE TIPO ESTÁ LIBERADO NO HORÁRIO ATUAL
            validacao = validar_tipo_registro_por_horario('saida_almoco', horarios)
            if validacao['horario_liberado']:
                tipos_liberados.append(tipo)
        
        if horarios['entrada_tarde']:
            tipo = {
                'value': 'entrada_tarde',
                'text': f'Retorno Almoço ({horarios_formatados["entrada_tarde"]})',
                'tolerancia': horarios_com_tolerancia.get('entrada_tarde', {}).get('mensagem', '')
            }
            todos_tipos.append(tipo)
            
            # ✅ VALIDAR SE ESTE TIPO ESTÁ LIBERADO NO HORÁRIO ATUAL
            validacao = validar_tipo_registro_por_horario('entrada_tarde', horarios)
            if validacao['horario_liberado']:
                tipos_liberados.append(tipo)
        
        if horarios['saida']:
            tipo = {
                'value': 'saida',
                'text': f'Fim Expediente ({horarios_formatados["saida"]})',
                'tolerancia': horarios_com_tolerancia.get('saida', {}).get('mensagem', '')
            }
            todos_tipos.append(tipo)
            
            # ✅ VALIDAR SE ESTE TIPO ESTÁ LIBERADO NO HORÁRIO ATUAL
            validacao = validar_tipo_registro_por_horario('saida', horarios)
            if validacao['horario_liberado']:
                tipos_liberados.append(tipo)
        
        # ✅ MELHORIA: Verificar registros já existentes para hoje
        registros_existentes = []
        try:
            conn = get_db_connection()
            cursor = conn.cursor(DictCursor)
            
            data_atual = date.today()
            data_inicio = data_atual.strftime('%Y-%m-%d 00:00:00')
            data_fim = data_atual.strftime('%Y-%m-%d 23:59:59')
            
            cursor.execute("""
                SELECT 
                    tipo_registro, 
                    DATE_FORMAT(data_hora, '%H:%i') as hora,
                    status_pontualidade
                FROM registros_ponto 
                WHERE funcionario_id = %s 
                AND data_hora BETWEEN %s AND %s
                ORDER BY data_hora ASC
            """, (funcionario_id, data_inicio, data_fim))
            
            for registro in cursor.fetchall():
                registros_existentes.append({
                    'tipo_registro': registro['tipo_registro'],
                    'hora': registro['hora'],
                    'status_pontualidade': registro['status_pontualidade'] or 'Registrado'
                })
                
                # Remover da lista de tipos disponíveis
                tipos_liberados = [t for t in tipos_liberados if t['value'] != registro['tipo_registro']]
            
            conn.close()
        except Exception as e:
            logger.error(f"Erro ao verificar registros existentes: {str(e)}")
        
        # ✅ MELHORIA: Verificar registros ausentes
        ausencias = verificar_registros_ausentes(funcionario_id)
        
        return jsonify({
            'success': True,
            'horarios': horarios_formatados,
            'horarios_com_tolerancia': horarios_com_tolerancia,
            'tipos_disponiveis': tipos_liberados,
            'todos_tipos': todos_tipos,
            'hora_atual': hora_atual,
            'ausencias': ausencias,  # Incluir informações sobre ausências
            'registros_existentes': registros_existentes  # Incluir registros existentes
        })
        
    except Exception as e:
        logger.error(f"Erro ao obter horários do funcionário {funcionario_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter horários: {str(e)}'
        }), 500

# Duplicar API para o blueprint com underscore
@registro_ponto_underscore_bp.route('/api/obter-horarios/<int:funcionario_id>')
@require_login
def api_obter_horarios_underscore(funcionario_id):
    """Versão duplicada da API com underscore"""
    return api_obter_horarios(funcionario_id)

# ========================================
# TRATAMENTO DE ERROS
# ========================================

@registro_ponto_bp.errorhandler(404)
def handle_404(error):
    """Tratamento de erro 404 para o blueprint."""
    return jsonify({
        'success': False,
        'message': 'Endpoint não encontrado'
    }), 404

@registro_ponto_underscore_bp.errorhandler(404)
def handle_404_underscore(error):
    """Tratamento de erro 404 para o blueprint com underscore."""
    return jsonify({
        'success': False,
        'message': 'Endpoint não encontrado'
    }), 404

@registro_ponto_bp.errorhandler(500)
def handle_500(error):
    """Tratamento de erro 500 para o blueprint."""
    logger.error(f"Erro interno no blueprint registro_ponto: {str(error)}")
    return jsonify({
        'success': False,
        'message': 'Erro interno do servidor'
    }), 500

@registro_ponto_underscore_bp.errorhandler(500)
def handle_500_underscore(error):
    """Tratamento de erro 500 para o blueprint com underscore."""
    logger.error(f"Erro interno no blueprint registro_ponto_underscore: {str(error)}")
    return jsonify({
        'success': False,
        'message': 'Erro interno do servidor'
    }), 500

# ========================================
# FIM DO BLUEPRINT REGISTRO DE PONTO
# ======================================== 