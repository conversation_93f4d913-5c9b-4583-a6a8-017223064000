#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MÓDULO: EMPRESA PRINCIPAL E GESTÃO DE CLIENTES
Sistema: RLPONTO-WEB
Data: 03/07/2025
Descrição: Gerenciamento da empresa principal e relacionamento com clientes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session
from datetime import datetime, date
import logging
import json
from decimal import Decimal
from utils.database import DatabaseManager
from utils.auth import require_admin

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Blueprint para empresa principal
empresa_principal_bp = Blueprint('empresa_principal', __name__, url_prefix='/empresa-principal')

# Rota de teste simples
@empresa_principal_bp.route('/teste')
def teste():
    """Rota de teste para verificar se o blueprint está funcionando"""
    return jsonify({
        'status': 'ok',
        'message': 'Blueprint empresa_principal está funcionando!',
        'timestamp': datetime.now().isoformat()
    })

# ================================================================
# FUNÇÕES AUXILIARES
# ================================================================

def get_empresa_principal():
    """Obter dados da empresa principal"""
    try:
        sql = """
        SELECT e.*,
               COALESCE((SELECT COUNT(*) FROM empresa_clientes ec WHERE ec.empresa_principal_id = e.id AND ec.ativo = TRUE), 0) as total_clientes,
               COALESCE((SELECT COUNT(DISTINCT fa.funcionario_id) FROM funcionario_alocacoes fa
                INNER JOIN empresa_clientes ec ON fa.contrato_id = ec.id
                WHERE ec.empresa_principal_id = e.id AND fa.ativo = TRUE), 0) as funcionarios_alocados
        FROM empresas e
        WHERE e.empresa_principal = TRUE AND e.ativa = TRUE
        LIMIT 1
        """
        db = DatabaseManager()
        result = db.execute_query(sql)
        # Retornar o primeiro item da lista ou None se não houver resultado
        return result[0] if result and len(result) > 0 else None
    except Exception as e:
        logger.error(f"Erro ao obter empresa principal: {e}")
        return None

def definir_empresa_principal(empresa_id):
    """Definir uma empresa como principal"""
    try:
        logger.info(f"Iniciando definição da empresa {empresa_id} como principal")

        # Primeiro, remover flag de todas as empresas
        db = DatabaseManager()
        sql_remove = "UPDATE empresas SET empresa_principal = FALSE"
        logger.info(f"Executando: {sql_remove}")
        db.execute_query(sql_remove, fetch_all=False)
        logger.info("Flag removida de todas as empresas")

        # Depois, definir a nova empresa principal
        sql_define = """
        UPDATE empresas
        SET empresa_principal = TRUE, tipo_empresa = 'principal'
        WHERE id = %s AND ativa = TRUE
        """
        logger.info(f"Executando: {sql_define} com empresa_id={empresa_id}")
        result = db.execute_query(sql_define, (empresa_id,), fetch_all=False)
        logger.info(f"Resultado da atualização: {result}")

        logger.info(f"Empresa {empresa_id} definida como principal com sucesso")
        return True

    except Exception as e:
        logger.error(f"Erro ao definir empresa principal: {e}")
        import traceback
        logger.error(f"Traceback completo: {traceback.format_exc()}")
        return False

def get_empresas_disponiveis_para_cliente():
    """Obter empresas que podem ser clientes"""
    try:
        # Query simplificada sem LEFT JOIN
        sql = """
        SELECT e.id, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
               e.tipo_empresa, e.ativa,
               FALSE as ja_e_cliente
        FROM empresas e
        WHERE e.empresa_principal = FALSE AND e.ativa = TRUE
        ORDER BY e.razao_social
        """
        db = DatabaseManager()
        empresas = db.execute_query(sql)

        # Verificar quais já são clientes separadamente
        if empresas:
            for empresa in empresas:
                try:
                    sql_check = "SELECT COUNT(*) as count FROM empresa_clientes WHERE empresa_cliente_id = %s AND ativo = TRUE"
                    result = db.execute_query(sql_check, (empresa['id'],))
                    empresa['ja_e_cliente'] = result[0]['count'] > 0 if result else False
                except Exception as e:
                    logger.error(f"Erro ao verificar se empresa {empresa['id']} é cliente: {e}")
                    empresa['ja_e_cliente'] = False

        return empresas
    except Exception as e:
        logger.error(f"Erro ao obter empresas disponíveis: {e}")
        return []

def get_clientes_da_empresa_principal():
    """Obter todos os clientes da empresa principal"""
    try:
        # Query simplificada para evitar problemas de JOIN
        sql = """
        SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
               0 as funcionarios_alocados
        FROM empresa_clientes ec
        INNER JOIN empresas e ON ec.empresa_cliente_id = e.id
        INNER JOIN empresas ep ON ec.empresa_principal_id = ep.id
        WHERE ep.empresa_principal = TRUE AND ec.ativo = TRUE
        ORDER BY ec.data_inicio DESC
        """
        db = DatabaseManager()
        clientes = db.execute_query(sql)

        # Calcular funcionários alocados e jornadas separadamente para cada cliente
        if clientes:
            for cliente in clientes:
                try:
                    # ✅ CORREÇÃO: Contar funcionários alocados + funcionários cadastrados na empresa
                    sql_func = """
                    SELECT COUNT(DISTINCT funcionario_id) as count FROM (
                        -- Funcionários alocados via tabela funcionario_alocacoes
                        SELECT fa.funcionario_id
                        FROM funcionario_alocacoes fa
                        WHERE fa.contrato_id = %s AND fa.ativo = TRUE

                        UNION

                        -- Funcionários cadastrados diretamente na empresa
                        SELECT f.id as funcionario_id
                        FROM funcionarios f
                        WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
                    ) as todos_funcionarios
                    """
                    result_func = db.execute_query(sql_func, (cliente['id'], cliente['empresa_cliente_id']))
                    cliente['funcionarios_alocados'] = result_func[0]['count'] if result_func else 0

                except Exception as e:
                    logger.error(f"Erro ao calcular dados do cliente {cliente.get('id', 'N/A')}: {e}")
                    cliente['funcionarios_alocados'] = 0

        return clientes
    except Exception as e:
        logger.error(f"Erro ao obter clientes: {e}")
        return []

def adicionar_cliente(empresa_principal_id, dados_cliente):
    """Adicionar uma empresa como cliente"""
    try:
        logger.info(f"🔍 Verificando estrutura da tabela empresa_clientes...")

        # Primeiro, verificar se a tabela existe e quais campos tem
        db = DatabaseManager()

        try:
            describe_result = db.execute_query("DESCRIBE empresa_clientes")
            logger.info(f"📊 Estrutura da tabela empresa_clientes: {describe_result}")
        except Exception as desc_error:
            logger.error(f"❌ Erro ao verificar estrutura da tabela: {desc_error}")

        # Query simplificada primeiro para testar
        logger.info("🔍 Tentando inserção com campos básicos...")
        sql_simples = """
        INSERT INTO empresa_clientes
        (empresa_principal_id, empresa_cliente_id, data_inicio, status_contrato, ativo)
        VALUES (%s, %s, %s, %s, %s)
        """

        params_simples = (
            empresa_principal_id,
            dados_cliente['empresa_cliente_id'],
            dados_cliente['data_inicio'],
            dados_cliente.get('status_contrato', 'ativo'),
            True
        )

        logger.info(f"📊 Parâmetros simples: {params_simples}")
        result = db.execute_query(sql_simples, params_simples, fetch_all=False)

        logger.info(f"✅ Cliente {dados_cliente['empresa_cliente_id']} adicionado à empresa principal {empresa_principal_id}")
        return True

    except Exception as e:
        logger.error(f"❌ Erro ao adicionar cliente: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def editar_cliente_dados(cliente_id, dados):
    """Editar dados de um cliente"""
    try:
        logger.info(f"🔄 Editando cliente {cliente_id} com dados: {dados}")

        db = DatabaseManager()

        sql = """
        UPDATE empresa_clientes SET
            nome_contrato = %s,
            codigo_contrato = %s,
            descricao_projeto = %s,
            data_inicio = %s,
            data_fim = %s,
            valor_contrato = %s,
            status_contrato = %s,
            observacoes = %s,
            updated_at = NOW()
        WHERE id = %s
        """

        params = (
            dados.get('nome_contrato'),
            dados.get('codigo_contrato'),
            dados.get('descricao_projeto'),
            dados['data_inicio'],
            dados.get('data_fim'),
            dados.get('valor_contrato'),
            dados.get('status_contrato', 'ativo'),
            dados.get('observacoes'),
            cliente_id
        )

        result = db.execute_query(sql, params, fetch_all=False)

        logger.info(f"✅ Cliente {cliente_id} editado com sucesso")
        return True

    except Exception as e:
        logger.error(f"❌ Erro ao editar cliente {cliente_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def get_funcionarios_disponiveis():
    """Obter funcionários disponíveis para alocação"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            return []

        sql = """
        SELECT f.id, f.nome_completo, f.cpf, f.cargo, f.setor, f.status_cadastro,
               f.data_admissao, f.empresa_id,
               CASE WHEN fa.id IS NOT NULL THEN TRUE ELSE FALSE END as ja_alocado,
               fa.empresa_cliente_id as cliente_atual_id,
               e.razao_social as cliente_atual_nome
        FROM funcionarios f
        LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id AND fa.ativo = TRUE
        LEFT JOIN empresas e ON fa.empresa_cliente_id = e.id
        WHERE f.empresa_id = %s AND f.status_cadastro = 'Ativo'
        ORDER BY f.nome_completo
        """
        db = DatabaseManager()
        return db.execute_query(sql, (empresa_principal['id'],))
    except Exception as e:
        logger.error(f"Erro ao obter funcionários disponíveis: {e}")
        return []

# ================================================================
# ROTAS PRINCIPAIS
# ================================================================

@empresa_principal_bp.route('/')
def index():
    """Página principal da empresa principal"""
    try:
        logger.info("=== ACESSANDO PÁGINA PRINCIPAL EMPRESA PRINCIPAL ===")

        # Verificação simples de sessão
        if 'usuario' not in session:
            return redirect(url_for('login'))

        # Buscar empresa principal real
        try:
            db = DatabaseManager()
            sql_empresa = "SELECT * FROM empresas WHERE empresa_principal = TRUE AND ativa = TRUE LIMIT 1"
            empresa_result = db.execute_query(sql_empresa)
            empresa_principal = empresa_result[0] if empresa_result else None
            logger.info(f"Empresa principal encontrada: {empresa_principal is not None}")
        except Exception as e:
            logger.error(f"Erro ao buscar empresa principal: {e}")
            empresa_principal = None

        if not empresa_principal:
            # Se não há empresa principal, mostrar lista para definir
            try:
                sql_empresas = """
                SELECT id, razao_social, nome_fantasia, cnpj, telefone, email, ativa, data_cadastro
                FROM empresas
                WHERE ativa = TRUE
                ORDER BY razao_social
                """
                empresas = db.execute_query(sql_empresas)
                return render_template('empresa_principal/definir_principal.html',
                                     empresas=empresas,
                                     titulo='Definir Empresa Principal')
            except Exception as e:
                logger.error(f"Erro ao buscar empresas: {e}")
                # Fallback: usar dados fictícios
                empresa_principal = {
                    'id': 1,
                    'razao_social': 'Empresa Principal (Configuração Necessária)',
                    'nome_fantasia': 'Configure uma empresa principal',
                    'cnpj': '00.000.000/0001-00',
                    'telefone': '',
                    'email': ''
                }

        # Dados básicos para o dashboard (simplificados)
        try:
            # Buscar estatísticas de empresas
            # Total de empresas
            sql_total_empresas = "SELECT COUNT(*) as total FROM empresas WHERE ativa = TRUE"
            result_total = db.execute_query(sql_total_empresas)
            total_empresas = result_total[0]['total'] if result_total else 0

            # Empresas ativas (não principal)
            sql_empresas_ativas = "SELECT COUNT(*) as total FROM empresas WHERE ativa = TRUE AND empresa_principal = FALSE"
            result_ativas = db.execute_query(sql_empresas_ativas)
            empresas_ativas = result_ativas[0]['total'] if result_ativas else 0

            # Total de funcionários
            sql_total_funcionarios = "SELECT COUNT(*) as total FROM funcionarios WHERE status_cadastro = 'Ativo'"
            result_funcionarios = db.execute_query(sql_total_funcionarios)
            total_funcionarios = result_funcionarios[0]['total'] if result_funcionarios else 0

        except Exception as e:
            logger.warning(f"Erro ao buscar estatísticas: {e}")
            total_empresas = 0
            empresas_ativas = 0
            total_funcionarios = 0

        # Estatísticas básicas
        stats = {
            'total_empresas': total_empresas,
            'empresas_ativas': empresas_ativas,
            'total_funcionarios': total_funcionarios
        }

        context = {
            'titulo': 'Empresa Principal - Dashboard',
            'empresa_principal': empresa_principal,
            'stats': stats
        }

        logger.info("Renderizando dashboard...")
        return render_template('empresa_principal/dashboard.html', **context)

    except Exception as e:
        logger.error(f"ERRO CRÍTICO na página principal: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Retornar página de erro simples
        return f"""
        <html>
        <head><title>Erro - Empresa Principal</title></head>
        <body>
            <h1>Erro na Página Principal</h1>
            <p><strong>Erro:</strong> {str(e)}</p>
            <p><a href="/">Voltar ao Início</a></p>
            <pre>{traceback.format_exc()}</pre>
        </body>
        </html>
        """, 500

@empresa_principal_bp.route('/definir', methods=['POST'])
def definir_principal():
    """Define uma empresa como principal"""
    try:
        data = request.get_json()
        empresa_id = data.get('empresa_id')

        if not empresa_id:
            return jsonify({'success': False, 'message': 'ID da empresa é obrigatório'})

        db = DatabaseManager()

        # Primeiro, remover flag de principal de todas as empresas
        sql_remove = "UPDATE empresas SET empresa_principal = FALSE"
        db.execute_query(sql_remove)

        # Definir a empresa selecionada como principal
        sql_define = "UPDATE empresas SET empresa_principal = TRUE WHERE id = %s"
        db.execute_query(sql_define, (empresa_id,))

        logger.info(f"Empresa {empresa_id} definida como principal")
        return jsonify({'success': True, 'message': 'Empresa principal definida com sucesso'})

    except Exception as e:
        logger.error(f"Erro ao definir empresa principal: {e}")
        return jsonify({'success': False, 'message': str(e)})

@empresa_principal_bp.route('/definir-principal', methods=['POST'])
def definir_principal_form():
    """Definir uma empresa como principal via formulário"""
    try:
        # Verificação simples de sessão
        if 'usuario' not in session:
            return redirect(url_for('login'))
        empresa_id = request.form.get('empresa_id')

        if not empresa_id:
            flash('ID da empresa não informado', 'error')
            return redirect(url_for('empresa_principal.index'))

        if definir_empresa_principal(empresa_id):
            flash('Empresa principal definida com sucesso!', 'success')
        else:
            flash('Erro ao definir empresa principal', 'error')

        return redirect(url_for('empresa_principal.index'))

    except Exception as e:
        logger.error(f"Erro ao definir empresa principal: {e}")
        flash('Erro interno do servidor', 'error')
        return redirect(url_for('empresa_principal.index'))

@empresa_principal_bp.route('/definir/<int:empresa_id>', methods=['POST'])
def definir_principal_old(empresa_id):
    """Definir uma empresa como principal"""
    try:
        if definir_empresa_principal(empresa_id):
            flash('Empresa principal definida com sucesso!', 'success')
        else:
            flash('Erro ao definir empresa principal', 'error')
            
    except Exception as e:
        logger.error(f"Erro ao definir empresa principal: {e}")
        flash('Erro interno do servidor', 'error')
    
    return redirect(url_for('empresa_principal.index'))

@empresa_principal_bp.route('/clientes')
@require_admin
def clientes():
    """Listar todos os clientes da empresa principal"""
    try:
        logger.info("=== INICIANDO LISTAGEM DE CLIENTES ===")

        # Buscar empresa principal diretamente do banco
        db = DatabaseManager()
        logger.info("✅ Conexão com banco estabelecida")

        # Query simplificada para empresa principal
        logger.info("🔍 Buscando empresa principal...")
        empresa_result = db.execute_query("""
            SELECT id, razao_social, nome_fantasia, cnpj, telefone, email
            FROM empresas
            WHERE empresa_principal = TRUE AND ativa = TRUE
            LIMIT 1
        """)
        logger.info(f"📊 Resultado empresa principal: {len(empresa_result) if empresa_result else 0} registros")

        if not empresa_result:
            logger.warning("⚠️ Nenhuma empresa principal encontrada")
            flash('Nenhuma empresa principal foi definida. Configure primeiro a empresa principal.', 'warning')
            return redirect(url_for('empresa_principal.index'))

        empresa_principal = empresa_result[0]
        logger.info(f"✅ Empresa principal: {empresa_principal.get('razao_social', 'N/A')}")

        # ✅ CORREÇÃO: Buscar clientes com funcionários alocados + cadastrados
        logger.info("🔍 Buscando clientes da empresa principal...")
        clientes_result = db.execute_query("""
            SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email,
                   COALESCE(funcionarios_alocados.total, 0) + COALESCE(funcionarios_cadastrados.total, 0) as funcionarios_alocados
            FROM empresa_clientes ec
            INNER JOIN empresas e ON ec.empresa_cliente_id = e.id

            -- Funcionários alocados via tabela funcionario_alocacoes
            LEFT JOIN (
                SELECT fa.empresa_cliente_id, COUNT(DISTINCT fa.funcionario_id) as total
                FROM funcionario_alocacoes fa
                WHERE fa.ativo = TRUE
                GROUP BY fa.empresa_cliente_id
            ) funcionarios_alocados ON ec.empresa_cliente_id = funcionarios_alocados.empresa_cliente_id

            -- Funcionários cadastrados diretamente na empresa
            LEFT JOIN (
                SELECT f.empresa_id, COUNT(DISTINCT f.id) as total
                FROM funcionarios f
                WHERE f.status_cadastro = 'Ativo'
                GROUP BY f.empresa_id
            ) funcionarios_cadastrados ON ec.empresa_cliente_id = funcionarios_cadastrados.empresa_id

            WHERE ec.empresa_principal_id = %s AND ec.ativo = TRUE
            ORDER BY e.razao_social
        """, (empresa_principal['id'],))

        clientes = clientes_result if clientes_result else []
        logger.info(f"📊 Clientes encontrados: {len(clientes)}")

        # Buscar empresas disponíveis para adicionar como clientes
        logger.info("🔍 Buscando empresas disponíveis...")
        empresas_result = db.execute_query("""
            SELECT e.id, e.razao_social, e.nome_fantasia, e.cnpj
            FROM empresas e
            WHERE e.ativa = TRUE
            AND e.empresa_principal = FALSE
            AND e.id NOT IN (
                SELECT DISTINCT ec.empresa_cliente_id
                FROM empresa_clientes ec
                WHERE ec.empresa_principal_id = %s AND ec.ativo = TRUE
            )
            ORDER BY e.razao_social
        """, (empresa_principal['id'],))

        empresas_disponiveis = empresas_result if empresas_result else []
        logger.info(f"📊 Empresas disponíveis: {len(empresas_disponiveis)}")

        # Calcular estatísticas para o template
        total_clientes = len(clientes)
        clientes_ativos = len([c for c in clientes if c.get('status_contrato') == 'ativo'])
        funcionarios_alocados = sum([c.get('funcionarios_alocados', 0) for c in clientes])

        stats = {
            'total_clientes': total_clientes,
            'clientes_ativos': clientes_ativos,
            'funcionarios_alocados': funcionarios_alocados
        }

        # Atualizar empresa_principal com estatísticas
        empresa_principal['total_clientes'] = total_clientes
        empresa_principal['funcionarios_alocados'] = funcionarios_alocados

        logger.info(f"📊 Estatísticas calculadas: {stats}")
        logger.info("✅ Renderizando template clientes.html")
        return render_template('empresa_principal/clientes.html',
                             empresa_principal=empresa_principal,
                             clientes=clientes,
                             empresas_disponiveis=empresas_disponiveis,
                             stats=stats)

    except Exception as e:
        logger.error(f"❌ ERRO na listagem de clientes: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        flash(f'Erro ao carregar lista de clientes: {str(e)}', 'error')
        return redirect(url_for('empresa_principal.index'))

@empresa_principal_bp.route('/clientes/adicionar', methods=['POST'])
def adicionar_cliente_route():
    """Adicionar novo cliente"""
    try:
        logger.info("=== INICIANDO ADIÇÃO DE CLIENTE ===")

        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            logger.error("❌ Empresa principal não definida")
            return jsonify({'success': False, 'message': 'Empresa principal não definida'})

        logger.info(f"✅ Empresa principal: {empresa_principal['razao_social']}")

        dados = {
            'empresa_cliente_id': request.form.get('empresa_cliente_id'),
            'nome_contrato': request.form.get('nome_contrato'),
            'codigo_contrato': request.form.get('codigo_contrato'),
            'descricao_projeto': request.form.get('descricao_projeto'),
            'data_inicio': request.form.get('data_inicio'),
            'data_fim': request.form.get('data_fim') if request.form.get('data_fim') else None,
            'valor_contrato': request.form.get('valor_contrato') if request.form.get('valor_contrato') else None,
            'status_contrato': request.form.get('status_contrato', 'ativo'),
            'observacoes': request.form.get('observacoes')
        }

        logger.info(f"📊 Dados recebidos: {dados}")

        # Validações
        if not dados['empresa_cliente_id'] or not dados['data_inicio']:
            logger.error("❌ Validação falhou: empresa_cliente_id ou data_inicio vazios")
            return jsonify({'success': False, 'message': 'Empresa e data de início são obrigatórios'})

        logger.info("✅ Validações passaram, tentando adicionar cliente...")

        if adicionar_cliente(empresa_principal['id'], dados):
            logger.info("✅ Cliente adicionado com sucesso!")
            return jsonify({'success': True, 'message': 'Cliente adicionado com sucesso!'})
        else:
            logger.error("❌ Falha na função adicionar_cliente")
            return jsonify({'success': False, 'message': 'Erro ao adicionar cliente'})

    except Exception as e:
        logger.error(f"❌ ERRO GERAL ao adicionar cliente: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': f'Erro interno: {str(e)}'})

# ================================================================
# API ENDPOINTS
# ================================================================

@empresa_principal_bp.route('/api/empresa-principal')
@require_admin
def api_empresa_principal():
    """API: Dados da empresa principal"""
    try:
        empresa = get_empresa_principal()
        if empresa:
            # Converter tipos não serializáveis
            for key, value in empresa.items():
                if isinstance(value, (date, datetime)):
                    empresa[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    empresa[key] = float(value)
            
        return jsonify({'success': True, 'data': empresa})
    except Exception as e:
        logger.error(f"Erro na API empresa principal: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/clientes')
@require_admin
def api_clientes():
    """API: Lista de clientes"""
    try:
        clientes = get_clientes_da_empresa_principal()
        
        # Converter tipos não serializáveis
        for cliente in clientes:
            for key, value in cliente.items():
                if isinstance(value, (date, datetime)):
                    cliente[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    cliente[key] = float(value)
        
        return jsonify({'success': True, 'data': clientes})
    except Exception as e:
        logger.error(f"Erro na API clientes: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/funcionarios-disponiveis')
def get_funcionarios_disponiveis_api():
    """API: Funcionários disponíveis para alocação"""
    try:
        funcionarios = get_funcionarios_disponiveis()
        
        # Converter tipos não serializáveis
        for funcionario in funcionarios:
            for key, value in funcionario.items():
                if isinstance(value, (date, datetime)):
                    funcionario[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    funcionario[key] = float(value)
        
        return jsonify({'success': True, 'funcionarios': funcionarios})
    except Exception as e:
        logger.error(f"Erro na API funcionários: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/empresas-disponiveis')
def get_empresas_disponiveis():
    """API: Empresas disponíveis para se tornarem clientes"""
    try:
        empresas = get_empresas_disponiveis_para_cliente()

        # Converter tipos não serializáveis
        for empresa in empresas:
            for key, value in empresa.items():
                if isinstance(value, (date, datetime)):
                    empresa[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    empresa[key] = float(value)

        return jsonify({'success': True, 'empresas': empresas})
    except Exception as e:
        logger.error(f"Erro na API empresas disponíveis: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/api/clientes-para-alocacao')
def get_clientes_para_alocacao():
    """API: Clientes disponíveis para alocação de funcionários"""
    try:
        clientes = get_clientes_da_empresa_principal()

        # Converter tipos não serializáveis
        for cliente in clientes:
            for key, value in cliente.items():
                if isinstance(value, (date, datetime)):
                    cliente[key] = value.isoformat()
                elif isinstance(value, Decimal):
                    cliente[key] = float(value)

        return jsonify({'success': True, 'clientes': clientes})
    except Exception as e:
        logger.error(f"Erro na API clientes para alocação: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/clientes/<int:cliente_id>/detalhes')
@require_admin
def detalhes_cliente(cliente_id):
    """Detalhes completos de um cliente"""
    try:
        logger.info(f"🔍 Buscando detalhes do cliente ID: {cliente_id}")
        db = DatabaseManager()

        # ✅ CORREÇÃO: Buscar dados do cliente sem campos inexistentes
        sql_cliente = """
        SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj, e.telefone, e.email
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        WHERE ec.id = %s
        """

        logger.info(f"📊 Executando query para cliente: {sql_cliente}")
        cliente = db.execute_query(sql_cliente, (cliente_id,))

        if not cliente:
            logger.warning(f"❌ Cliente {cliente_id} não encontrado")
            return jsonify({'success': False, 'message': 'Cliente não encontrado'})

        cliente = cliente[0]
        logger.info(f"✅ Cliente encontrado: {cliente['razao_social']}")

        # ✅ CORREÇÃO: Buscar funcionários alocados + funcionários cadastrados na empresa
        sql_funcionarios = """
        SELECT
            f.id as funcionario_id,
            f.nome_completo as nome,
            f.cargo,
            f.cpf,
            f.telefone1 as telefone,
            COALESCE(jt_alocacao.nome_jornada, jt_empresa.nome_jornada) as jornada_nome,
            -- ✅ CORREÇÃO: Calcular carga horária baseada nos horários
            CASE
                WHEN COALESCE(jt_alocacao.seg_qui_entrada, jt_empresa.seg_qui_entrada) IS NOT NULL
                     AND COALESCE(jt_alocacao.seg_qui_saida, jt_empresa.seg_qui_saida) IS NOT NULL
                THEN ROUND(
                    TIME_TO_SEC(TIMEDIFF(
                        COALESCE(jt_alocacao.seg_qui_saida, jt_empresa.seg_qui_saida),
                        COALESCE(jt_alocacao.seg_qui_entrada, jt_empresa.seg_qui_entrada)
                    )) / 3600, 1
                )
                ELSE NULL
            END as carga_horaria,
            CASE
                WHEN fa.id IS NOT NULL THEN 'Alocado'
                ELSE 'Funcionário da Empresa'
            END as tipo_vinculo,
            fa.data_inicio,
            fa.data_fim,
            fa.percentual_alocacao,
            fa.valor_hora,
            fa.ativo as alocacao_ativa
        FROM funcionarios f

        -- Alocações ativas (se existir)
        LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id
            AND fa.empresa_cliente_id = %s AND fa.ativo = TRUE

        -- Jornada da alocação
        LEFT JOIN jornadas_trabalho jt_alocacao ON fa.jornada_trabalho_id = jt_alocacao.id

        -- Jornada da empresa (fallback)
        LEFT JOIN jornadas_trabalho jt_empresa ON f.jornada_trabalho_id = jt_empresa.id

        WHERE (
            -- Funcionários alocados para esta empresa
            fa.empresa_cliente_id = %s AND fa.ativo = TRUE
        ) OR (
            -- Funcionários cadastrados diretamente nesta empresa
            f.empresa_id = %s AND f.status_cadastro = 'Ativo'
        )

        ORDER BY f.nome_completo
        """

        funcionarios = db.execute_query(sql_funcionarios, (
            cliente['empresa_cliente_id'],
            cliente['empresa_cliente_id'],
            cliente['empresa_cliente_id']
        ))

        # Renderizar HTML dos detalhes
        html = render_template('empresa_principal/detalhes_cliente_modal.html',
                             cliente=cliente, funcionarios=funcionarios)

        return jsonify({'success': True, 'html': html})

    except Exception as e:
        logger.error(f"Erro ao buscar detalhes do cliente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@empresa_principal_bp.route('/clientes/<int:cliente_id>/dados')
@require_admin
def get_cliente_dados(cliente_id):
    """Obter dados do cliente para edição"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT ec.*, e.razao_social, e.nome_fantasia, e.cnpj
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        WHERE ec.id = %s
        """

        cliente = db.execute_query(sql, (cliente_id,), fetch_one=True)

        if not cliente:
            return jsonify({'success': False, 'message': 'Cliente não encontrado'})

        # Converter datas para string para JSON
        if cliente.get('data_inicio'):
            cliente['data_inicio'] = cliente['data_inicio'].strftime('%Y-%m-%d')
        if cliente.get('data_fim'):
            cliente['data_fim'] = cliente['data_fim'].strftime('%Y-%m-%d')

        return jsonify({'success': True, 'cliente': cliente})

    except Exception as e:
        logger.error(f"Erro ao obter dados do cliente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@empresa_principal_bp.route('/clientes/<int:cliente_id>/editar', methods=['POST'])
@require_admin
def editar_cliente(cliente_id):
    """Editar dados do cliente"""
    try:
        dados = {
            'nome_contrato': request.form.get('nome_contrato'),
            'codigo_contrato': request.form.get('codigo_contrato'),
            'descricao_projeto': request.form.get('descricao_projeto'),
            'data_inicio': request.form.get('data_inicio'),
            'data_fim': request.form.get('data_fim') if request.form.get('data_fim') else None,
            'valor_contrato': request.form.get('valor_contrato') if request.form.get('valor_contrato') else None,
            'status_contrato': request.form.get('status_contrato', 'ativo'),
            'observacoes': request.form.get('observacoes')
        }

        # Validação
        if not dados['data_inicio']:
            return jsonify({'success': False, 'message': 'Data de início é obrigatória'})

        # Atualizar cliente
        if editar_cliente_dados(cliente_id, dados):
            return jsonify({'success': True, 'message': 'Cliente editado com sucesso!'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao editar cliente'})

    except Exception as e:
        logger.error(f"Erro ao editar cliente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@empresa_principal_bp.route('/clientes/<int:cliente_id>/status', methods=['POST'])
@require_admin
def alterar_status_contrato(cliente_id):
    """Alterar status do contrato (pausar/reativar)"""
    try:
        data = request.get_json()
        novo_status = data.get('status')

        if novo_status not in ['ativo', 'pausado', 'finalizado', 'cancelado']:
            return jsonify({'success': False, 'message': 'Status inválido'})

        db = DatabaseManager()

        sql = """
        UPDATE empresa_clientes SET
            status_contrato = %s,
            updated_at = NOW()
        WHERE id = %s
        """

        result = db.execute_query(sql, (novo_status, cliente_id), fetch_all=False)

        logger.info(f"✅ Status do cliente {cliente_id} alterado para: {novo_status}")
        return jsonify({'success': True, 'message': f'Status alterado para {novo_status} com sucesso!'})

    except Exception as e:
        logger.error(f"Erro ao alterar status do cliente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno do servidor'})

@empresa_principal_bp.route('/funcionarios/alocar', methods=['POST'])
def alocar_funcionario_route():
    """Alocar funcionário a um cliente"""
    try:
        dados = {
            'funcionario_id': request.form.get('funcionario_id'),
            'empresa_cliente_id': request.form.get('empresa_cliente_id'),
            'jornada_trabalho_id': request.form.get('jornada_trabalho_id'),
            'cargo_no_cliente': request.form.get('cargo_no_cliente'),
            'data_inicio': request.form.get('data_inicio'),
            'data_fim': request.form.get('data_fim') if request.form.get('data_fim') else None,
            'percentual_alocacao': request.form.get('percentual_alocacao', 100),
            'valor_hora': request.form.get('valor_hora') if request.form.get('valor_hora') else None,
            'observacoes': request.form.get('observacoes')
        }

        # Validações
        if not all([dados['funcionario_id'], dados['empresa_cliente_id'],
                   dados['jornada_trabalho_id'], dados['data_inicio']]):
            return jsonify({'success': False, 'message': 'Campos obrigatórios não preenchidos'})

        db = DatabaseManager()

        # Verificar se funcionário já está alocado para este cliente
        sql_check = """
        SELECT id FROM funcionario_alocacoes
        WHERE funcionario_id = %s AND empresa_cliente_id = %s
        AND ativo = 1 AND (data_fim IS NULL OR data_fim >= CURDATE())
        """

        existing = db.execute_query(sql_check, (dados['funcionario_id'], dados['empresa_cliente_id']))
        if existing:
            return jsonify({'success': False, 'message': 'Funcionário já está alocado para este cliente'})

        # Inserir alocação
        sql_insert = """
        INSERT INTO funcionario_alocacoes
        (funcionario_id, empresa_cliente_id, jornada_trabalho_id, cargo_no_cliente,
         data_inicio, data_fim, percentual_alocacao, valor_hora, observacoes, ativo)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, 1)
        """

        params = (
            dados['funcionario_id'], dados['empresa_cliente_id'], dados['jornada_trabalho_id'],
            dados['cargo_no_cliente'], dados['data_inicio'], dados['data_fim'],
            dados['percentual_alocacao'], dados['valor_hora'], dados['observacoes']
        )

        result = db.execute_query(sql_insert, params, fetch_all=False)
        if result is not None:
            return jsonify({'success': True, 'message': 'Funcionário alocado com sucesso!'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao alocar funcionário'})

    except Exception as e:
        logger.error(f"Erro ao alocar funcionário: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/clientes/alterar-status', methods=['POST'])
@require_admin
def alterar_status_cliente():
    """Alterar status de um contrato"""
    try:
        data = request.get_json()
        cliente_id = data.get('cliente_id')
        novo_status = data.get('status')

        if not cliente_id or not novo_status:
            return jsonify({'success': False, 'message': 'Dados incompletos'})

        if novo_status not in ['ativo', 'pausado', 'finalizado', 'cancelado']:
            return jsonify({'success': False, 'message': 'Status inválido'})

        db = DatabaseManager()

        sql = """
        UPDATE empresa_clientes
        SET status_contrato = %s, updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """

        if db.execute_update(sql, (novo_status, cliente_id)):
            return jsonify({'success': True, 'message': f'Status alterado para {novo_status}'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao alterar status'})

    except Exception as e:
        logger.error(f"Erro ao alterar status: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/funcionarios')
@require_admin
def funcionarios():
    """Página de gestão de funcionários da empresa principal"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            flash('Empresa principal não definida', 'error')
            return redirect(url_for('empresa_principal.index'))

        # Buscar funcionários da empresa principal
        funcionarios = get_funcionarios_empresa_principal()

        # Calcular estatísticas
        stats = {
            'total_funcionarios': len(funcionarios) if funcionarios else 0,
            'funcionarios_ativos': len([f for f in funcionarios if f.get('status_cadastro') == 'Ativo']) if funcionarios else 0
        }

        context = {
            'titulo': 'Gestão de Funcionários',
            'empresa_principal': empresa_principal,
            'funcionarios': funcionarios,
            'stats': stats
        }

        return render_template('empresa_principal/funcionarios.html', **context)

    except Exception as e:
        import traceback
        logger.error(f"❌ ERRO DETALHADO NA PÁGINA DE FUNCIONÁRIOS:")
        logger.error(f"   Tipo do erro: {type(e).__name__}")
        logger.error(f"   Mensagem: {str(e)}")
        logger.error(f"   Traceback completo: {traceback.format_exc()}")

        flash('Erro ao carregar funcionários - verifique os logs para detalhes', 'error')
        return redirect(url_for('empresa_principal.index'))

def get_funcionarios_empresa_principal():
    """Obter todos os funcionários da empresa principal (diretos + alocados)"""
    try:
        db = DatabaseManager()

        # Buscar empresa principal
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            return []

        # ✅ CORREÇÃO CRÍTICA: Incluir funcionários diretos E alocados para empresa principal
        sql = """
        SELECT f.*,
               e.razao_social as empresa_nome,
               -- ✅ PRIORIDADE: Jornada da alocação se existir, senão jornada padrão da empresa do funcionário
               COALESCE(jt_alocacao.nome_jornada, jt_empresa_padrao.nome_jornada, jt_funcionario.nome_jornada) as jornada_nome,
               COALESCE(jt_alocacao.seg_qui_entrada, jt_empresa_padrao.seg_qui_entrada, jt_funcionario.seg_qui_entrada) as seg_qui_entrada,
               COALESCE(jt_alocacao.seg_qui_saida, jt_empresa_padrao.seg_qui_saida, jt_funcionario.seg_qui_saida) as seg_qui_saida,
               COALESCE(jt_alocacao.sexta_entrada, jt_empresa_padrao.sexta_entrada, jt_funcionario.sexta_entrada) as sexta_entrada,
               COALESCE(jt_alocacao.sexta_saida, jt_empresa_padrao.sexta_saida, jt_funcionario.sexta_saida) as sexta_saida,
               f.matricula_empresa as matricula,
               CASE
                   WHEN f.status_cadastro = 'Ativo' THEN 'Ativo'
                   ELSE 'Inativo'
               END as status_display,
               DATE_FORMAT(f.data_admissao, '%%d/%%m/%%Y') as data_admissao_formatada,
               DATEDIFF(CURDATE(), f.data_admissao) as dias_empresa,
               -- ✅ Identificar tipo de vínculo
               CASE
                   WHEN fa.id IS NOT NULL THEN 'Alocado'
                   ELSE 'Funcionário da Empresa'
               END as tipo_vinculo
        FROM funcionarios f
        JOIN empresas e ON f.empresa_id = e.id

        -- ✅ Alocações ativas para empresa principal
        LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id
            AND fa.empresa_cliente_id = %s AND fa.ativo = TRUE

        -- ✅ Jornada da alocação (prioridade máxima)
        LEFT JOIN jornadas_trabalho jt_alocacao ON fa.jornada_trabalho_id = jt_alocacao.id

        -- ✅ Jornada padrão da empresa do funcionário (prioridade média)
        LEFT JOIN jornadas_trabalho jt_empresa_padrao ON f.empresa_id = jt_empresa_padrao.empresa_id
            AND jt_empresa_padrao.padrao = 1 AND jt_empresa_padrao.ativa = 1

        -- ✅ Jornada específica do funcionário (fallback)
        LEFT JOIN jornadas_trabalho jt_funcionario ON f.jornada_trabalho_id = jt_funcionario.id

        WHERE (
            -- Funcionários diretos da empresa principal
            e.empresa_principal = TRUE
        ) OR (
            -- Funcionários alocados para empresa principal
            fa.empresa_cliente_id = %s AND fa.ativo = TRUE
        )
        ORDER BY f.nome_completo
        """

        funcionarios = db.execute_query(sql, (empresa_principal['id'], empresa_principal['id']))
        
        # Log para debug
        if funcionarios:
            logger.info(f"✅ Encontrados {len(funcionarios)} funcionários da empresa principal")
            for func in funcionarios[:2]:  # Log apenas os primeiros 2 para não poluir
                logger.info(f"🔍 Funcionário: {func.get('nome_completo')} - Matrícula: {func.get('matricula', 'N/A')}")
        else:
            logger.warning("⚠️ Nenhum funcionário encontrado para empresa principal")

        return funcionarios

    except Exception as e:
        logger.error(f"Erro ao buscar funcionários da empresa principal: {e}")
        return []

@empresa_principal_bp.route('/api/test')
def api_test():
    """API de teste"""
    return jsonify({'success': True, 'message': 'API funcionando', 'endpoint': request.endpoint})

# ===== ROTAS DE RELATÓRIOS =====

@empresa_principal_bp.route('/relatorios')
def relatorios():
    """Página de relatórios e análises"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            flash('Empresa principal não definida', 'error')
            return redirect(url_for('empresa_principal.index'))

        # Buscar dados para relatórios
        clientes = get_clientes_da_empresa_principal()
        alocacoes = get_todas_alocacoes()

        # Calcular estatísticas
        stats = calcular_estatisticas_relatorios()

        # Top clientes
        top_clientes = get_top_clientes()

        # Resumo financeiro
        resumo_financeiro = calcular_resumo_financeiro()

        # Dados para gráficos
        dados_graficos = gerar_dados_graficos()

        # Alertas
        alertas = gerar_alertas_sistema()

        context = {
            'titulo': 'Relatórios e Análises',
            'empresa_principal': empresa_principal,
            'clientes': clientes,
            'stats': stats,
            'top_clientes': top_clientes,
            'resumo_financeiro': resumo_financeiro,
            'dados_graficos': dados_graficos,
            'alertas': alertas
        }

        return render_template('empresa_principal/relatorios.html', **context)

    except Exception as e:
        logger.error(f"Erro na página de relatórios: {e}")
        flash('Erro ao carregar relatórios', 'error')
        return redirect(url_for('empresa_principal.index'))

@empresa_principal_bp.route('/alocacoes/alterar-status', methods=['POST'])
@require_admin
def alterar_status_alocacao():
    """Alterar status de uma alocação"""
    try:
        data = request.get_json()
        alocacao_id = data.get('alocacao_id')
        ativo = data.get('ativo')

        if alocacao_id is None or ativo is None:
            return jsonify({'success': False, 'message': 'Dados incompletos'})

        db = DatabaseManager()

        sql = """
        UPDATE funcionario_alocacoes
        SET ativo = %s, updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """

        result = db.execute_query(sql, (ativo, alocacao_id), fetch_all=False)
        if result is not None:
            status_texto = 'ativada' if ativo else 'desativada'
            return jsonify({'success': True, 'message': f'Alocação {status_texto} com sucesso'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao alterar status da alocação'})

    except Exception as e:
        logger.error(f"Erro ao alterar status da alocação: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

        params = (
            dados['empresa_cliente_id'], dados['nome_jornada'], dados['tipo_jornada'],
            dados['categoria_funcionario'], dados['descricao'], dados['seg_qui_entrada'],
            dados['seg_qui_saida'], dados['sexta_entrada'], dados['sexta_saida'],
            dados['sabado_entrada'], dados['sabado_saida'], dados['intervalo_inicio'],
            dados['intervalo_fim'], dados['tolerancia_entrada_minutos'], dados['padrao']
        )

        jornada_id = db.execute_insert(sql_insert, params)

        if jornada_id:
            # Se deve aplicar automaticamente, aplicar aos funcionários alocados
            if dados['aplicar_automaticamente']:
                aplicar_jornada_aos_funcionarios(dados['empresa_cliente_id'], jornada_id)

            return jsonify({'success': True, 'message': 'Jornada criada com sucesso!', 'jornada_id': jornada_id})
        else:
            return jsonify({'success': False, 'message': 'Erro ao criar jornada'})

    except Exception as e:
        logger.error(f"Erro ao criar jornada: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

    """Aplicar jornada específica aos funcionários de um cliente"""
    try:
        data = request.get_json()
        cliente_id = data.get('cliente_id')
        jornada_id = data.get('jornada_id')

        if not cliente_id or not jornada_id:
            return jsonify({'success': False, 'message': 'Dados incompletos'})

        funcionarios_afetados = aplicar_jornada_aos_funcionarios(cliente_id, jornada_id)

        return jsonify({
            'success': True,
            'message': f'Jornada aplicada a {funcionarios_afetados} funcionários',
            'funcionarios_afetados': funcionarios_afetados
        })

    except Exception as e:
        logger.error(f"Erro ao aplicar jornada: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

    """Aplicar jornada aos funcionários alocados de um cliente"""
    try:
        db = DatabaseManager()

        # Atualizar alocações ativas do cliente
        sql_update = """
        UPDATE funcionario_alocacoes
        SET jornada_trabalho_id = %s, updated_at = CURRENT_TIMESTAMP
        WHERE empresa_cliente_id = %s AND ativo = 1
        """

        result = db.execute_query(sql_update, (jornada_id, cliente_id), fetch_all=False)

        # Contar funcionários afetados
        sql_count = """
        SELECT COUNT(*) as total
        FROM funcionario_alocacoes
        WHERE empresa_cliente_id = %s AND ativo = 1 AND jornada_trabalho_id = %s
        """

        count_result = db.execute_query(sql_count, (cliente_id, jornada_id))
        funcionarios_afetados = count_result[0]['total'] if count_result else 0

        return funcionarios_afetados

    except Exception as e:
        logger.error(f"Erro ao aplicar jornada aos funcionários: {e}")
        return 0

    """Aplicar jornadas padrão automaticamente a todos os clientes"""
    try:
        db = DatabaseManager()

        # Buscar clientes com jornadas padrão
        sql_clientes_jornadas = """
        SELECT ec.empresa_cliente_id, j.id as jornada_id
        FROM empresa_clientes ec
        JOIN jornadas_trabalho j ON ec.empresa_cliente_id = j.empresa_id
        WHERE j.padrao = 1 AND j.ativa = 1
        """

        clientes_jornadas = db.execute_query(sql_clientes_jornadas)

        total_funcionarios = 0

        for item in clientes_jornadas:
            funcionarios_afetados = aplicar_jornada_aos_funcionarios(
                item['empresa_cliente_id'],
                item['jornada_id']
            )
            total_funcionarios += funcionarios_afetados

        return jsonify({
            'success': True,
            'message': f'Jornadas aplicadas automaticamente',
            'funcionarios_afetados': total_funcionarios,
            'clientes_processados': len(clientes_jornadas)
        })

    except Exception as e:
        logger.error(f"Erro ao aplicar jornadas automaticamente: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

    """Definir uma jornada como padrão para seu cliente"""
    try:
        data = request.get_json()
        jornada_id = data.get('jornada_id')

        if not jornada_id:
            return jsonify({'success': False, 'message': 'ID da jornada não informado'})

        db = DatabaseManager()

        # Buscar empresa da jornada
        sql_empresa = """
        SELECT empresa_id FROM jornadas_trabalho WHERE id = %s
        """

        empresa_result = db.execute_query(sql_empresa, (jornada_id,))
        if not empresa_result:
            return jsonify({'success': False, 'message': 'Jornada não encontrada'})

        empresa_id = empresa_result[0]['empresa_id']

        # Remover padrão das outras jornadas da mesma empresa
        sql_remove_padrao = """
        UPDATE jornadas_trabalho
        SET padrao = 0
        WHERE empresa_id = %s
        """
        db.execute_update(sql_remove_padrao, (empresa_id,))

        # Definir nova jornada padrão
        sql_set_padrao = """
        UPDATE jornadas_trabalho
        SET padrao = 1
        WHERE id = %s
        """

        if db.execute_update(sql_set_padrao, (jornada_id,)):
            return jsonify({'success': True, 'message': 'Jornada definida como padrão'})
        else:
            return jsonify({'success': False, 'message': 'Erro ao definir jornada padrão'})

    except Exception as e:
        logger.error(f"Erro ao definir jornada padrão: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/relatorios')
def relatorios():
    """Página de relatórios e análises"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            flash('Empresa principal não definida', 'error')
            return redirect(url_for('empresa_principal.index'))

        # Buscar dados para relatórios
        clientes = get_clientes_da_empresa_principal()
        alocacoes = get_todas_alocacoes()

        # Calcular estatísticas
        stats = calcular_estatisticas_relatorios()

        # Top clientes
        top_clientes = get_top_clientes()

        # Resumo financeiro
        resumo_financeiro = calcular_resumo_financeiro()

        # Dados para gráficos
        dados_graficos = gerar_dados_graficos()

        # Alertas
        alertas = gerar_alertas_sistema()

        context = {
            'titulo': 'Relatórios e Análises',
            'empresa_principal': empresa_principal,
            'clientes': clientes,
            'stats': stats,
            'top_clientes': top_clientes,
            'resumo_financeiro': resumo_financeiro,
            'dados_graficos': dados_graficos,
            'alertas': alertas
        }

        return render_template('empresa_principal/relatorios.html', **context)

    except Exception as e:
        logger.error(f"Erro na página de relatórios: {e}")
        flash('Erro ao carregar relatórios', 'error')
        return redirect(url_for('empresa_principal.index'))

def calcular_estatisticas_relatorios():
    """Calcular estatísticas para relatórios"""
    try:
        db = DatabaseManager()

        # Total de clientes
        sql_clientes = "SELECT COUNT(DISTINCT empresa_cliente_id) as total FROM empresa_clientes"
        result_clientes = db.execute_query(sql_clientes)
        total_clientes = result_clientes[0]['total'] if result_clientes else 0

        # Total de funcionários alocados
        sql_funcionarios = "SELECT COUNT(DISTINCT funcionario_id) as total FROM funcionario_alocacoes WHERE ativo = 1"
        result_funcionarios = db.execute_query(sql_funcionarios)
        total_funcionarios = result_funcionarios[0]['total'] if result_funcionarios else 0

        # Total de alocações ativas
        sql_alocacoes = "SELECT COUNT(*) as total FROM funcionario_alocacoes WHERE ativo = 1"
        result_alocacoes = db.execute_query(sql_alocacoes)
        total_alocacoes = result_alocacoes[0]['total'] if result_alocacoes else 0

        return {
            'total_clientes': total_clientes,
            'total_funcionarios': total_funcionarios,
            'total_alocacoes': total_alocacoes
        }

    except Exception as e:
        logger.error(f"Erro ao calcular estatísticas: {e}")
        return {'total_clientes': 0, 'total_funcionarios': 0, 'total_alocacoes': 0}

def get_top_clientes():
    """Obter top clientes por número de funcionários"""
    try:
        db = DatabaseManager()

        sql = """
        SELECT e.razao_social, ec.status_contrato,
               COUNT(fa.funcionario_id) as funcionarios_alocados
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = 1
        GROUP BY ec.empresa_cliente_id, e.razao_social, ec.status_contrato
        ORDER BY funcionarios_alocados DESC
        LIMIT 10
        """

        return db.execute_query(sql)

    except Exception as e:
        logger.error(f"Erro ao buscar top clientes: {e}")
        return []

def calcular_resumo_financeiro():
    """Calcular resumo financeiro"""
    try:
        db = DatabaseManager()

        # Valor total dos contratos
        sql_valor_total = """
        SELECT COALESCE(SUM(valor_contrato), 0) as total
        FROM empresa_clientes
        WHERE status_contrato = 'ativo' AND valor_contrato IS NOT NULL
        """
        result_total = db.execute_query(sql_valor_total)
        valor_total_contratos = float(result_total[0]['total']) if result_total else 0.0

        # Valor médio por hora
        sql_valor_hora = """
        SELECT AVG(valor_hora) as media
        FROM funcionario_alocacoes
        WHERE ativo = 1 AND valor_hora IS NOT NULL
        """
        result_hora = db.execute_query(sql_valor_hora)
        valor_medio_hora = float(result_hora[0]['media']) if result_hora and result_hora[0]['media'] else 0.0

        # Contratos ativos
        sql_contratos = """
        SELECT COUNT(*) as total
        FROM empresa_clientes
        WHERE status_contrato = 'ativo'
        """
        result_contratos = db.execute_query(sql_contratos)
        contratos_ativos = result_contratos[0]['total'] if result_contratos else 0

        # Estimativa de horas por mês (funcionários * 160 horas médias)
        sql_horas = """
        SELECT COUNT(*) * 160 as horas_estimadas
        FROM funcionario_alocacoes
        WHERE ativo = 1
        """
        result_horas = db.execute_query(sql_horas)
        horas_mes_estimadas = result_horas[0]['horas_estimadas'] if result_horas else 0

        return {
            'valor_total_contratos': valor_total_contratos,
            'valor_medio_hora': valor_medio_hora,
            'contratos_ativos': contratos_ativos,
            'horas_mes_estimadas': horas_mes_estimadas
        }

    except Exception as e:
        logger.error(f"Erro ao calcular resumo financeiro: {e}")
        return {
            'valor_total_contratos': 0.0,
            'valor_medio_hora': 0.0,
            'contratos_ativos': 0,
            'horas_mes_estimadas': 0
        }

def gerar_dados_graficos():
    """Gerar dados para os gráficos"""
    try:
        db = DatabaseManager()

        # Clientes por status
        sql_status = """
        SELECT status_contrato, COUNT(*) as total
        FROM empresa_clientes
        GROUP BY status_contrato
        """
        result_status = db.execute_query(sql_status)

        clientes_status = {
            'labels': [item['status_contrato'].title() for item in result_status] if result_status else [],
            'data': [item['total'] for item in result_status] if result_status else []
        }

        # Funcionários por cliente (top 10)
        sql_funcionarios = """
        SELECT e.razao_social, COUNT(fa.funcionario_id) as total
        FROM empresa_clientes ec
        JOIN empresas e ON ec.empresa_cliente_id = e.id
        LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = 1
        GROUP BY ec.empresa_cliente_id, e.razao_social
        ORDER BY total DESC
        LIMIT 10
        """
        result_funcionarios = db.execute_query(sql_funcionarios)

        funcionarios_cliente = {
            'labels': [item['razao_social'][:20] + '...' if len(item['razao_social']) > 20 else item['razao_social'] for item in result_funcionarios] if result_funcionarios else [],
            'data': [item['total'] for item in result_funcionarios] if result_funcionarios else []
        }

        # Evolução de alocações (últimos 6 meses)
        sql_evolucao = """
        SELECT DATE_FORMAT(created_at, '%Y-%m') as mes, COUNT(*) as total
        FROM funcionario_alocacoes
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY mes
        """
        result_evolucao = db.execute_query(sql_evolucao)

        evolucao = {
            'labels': [item['mes'] for item in result_evolucao] if result_evolucao else [],
            'data': [item['total'] for item in result_evolucao] if result_evolucao else []
        }

        return {
            'clientes_status': clientes_status,
            'funcionarios_cliente': funcionarios_cliente,
            'evolucao': evolucao
        }

    except Exception as e:
        logger.error(f"Erro ao gerar dados dos gráficos: {e}")
        return {
            'clientes_status': {'labels': [], 'data': []},
            'funcionarios_cliente': {'labels': [], 'data': []},
            'evolucao': {'labels': [], 'data': []}
        }

def gerar_alertas_sistema():
    """Gerar alertas do sistema"""
    try:
        alertas = []
        db = DatabaseManager()

        # Verificar contratos próximos do vencimento
        sql_vencimento = """
        SELECT COUNT(*) as total
        FROM empresa_clientes
        WHERE status_contrato = 'ativo'
        AND data_fim IS NOT NULL
        AND data_fim <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        """
        result_vencimento = db.execute_query(sql_vencimento)

        if result_vencimento and result_vencimento[0]['total'] > 0:
            alertas.append({
                'tipo': 'warning',
                'titulo': 'Contratos Vencendo',
                'mensagem': f"{result_vencimento[0]['total']} contrato(s) vencem nos próximos 30 dias"
            })

        # Verificar funcionários sem jornada definida
        sql_sem_jornada = """
        SELECT COUNT(*) as total
        FROM funcionario_alocacoes fa
        WHERE fa.ativo = 1 AND fa.jornada_trabalho_id IS NULL
        """
        result_sem_jornada = db.execute_query(sql_sem_jornada)

        if result_sem_jornada and result_sem_jornada[0]['total'] > 0:
            alertas.append({
                'tipo': 'info',
                'titulo': 'Jornadas Pendentes',
                'mensagem': f"{result_sem_jornada[0]['total']} funcionário(s) sem jornada definida"
            })

        # Verificar clientes sem funcionários alocados
        sql_sem_funcionarios = """
        SELECT COUNT(*) as total
        FROM empresa_clientes ec
        LEFT JOIN funcionario_alocacoes fa ON ec.empresa_cliente_id = fa.empresa_cliente_id AND fa.ativo = 1
        WHERE ec.status_contrato = 'ativo' AND fa.id IS NULL
        """
        result_sem_funcionarios = db.execute_query(sql_sem_funcionarios)

        if result_sem_funcionarios and result_sem_funcionarios[0]['total'] > 0:
            alertas.append({
                'tipo': 'warning',
                'titulo': 'Clientes sem Funcionários',
                'mensagem': f"{result_sem_funcionarios[0]['total']} cliente(s) ativo(s) sem funcionários alocados"
            })

        return alertas

    except Exception as e:
        logger.error(f"Erro ao gerar alertas: {e}")
        return []

@empresa_principal_bp.route('/relatorios/dados-graficos', methods=['POST'])
@require_admin
def dados_graficos():
    """API para atualizar dados dos gráficos com filtros"""
    try:
        # Aqui você pode implementar filtros específicos
        dados = gerar_dados_graficos()
        return jsonify({'success': True, 'dados': dados})

    except Exception as e:
        logger.error(f"Erro ao buscar dados dos gráficos: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

@empresa_principal_bp.route('/relatorios/gerar', methods=['POST'])
@require_admin
def gerar_relatorio():
    """Gerar relatório personalizado"""
    try:
        # Por enquanto, retornar um PDF simples
        # Em uma implementação completa, você usaria bibliotecas como ReportLab ou WeasyPrint

        from io import BytesIO

        # Simular geração de PDF
        buffer = BytesIO()
        buffer.write(b"Relatorio de Empresa Principal - Em desenvolvimento")
        buffer.seek(0)

        return buffer.getvalue(), 200, {
            'Content-Type': 'application/pdf',
            'Content-Disposition': 'attachment; filename=relatorio_empresa_principal.pdf'
        }

    except Exception as e:
        logger.error(f"Erro ao gerar relatório: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'})

# ===== ROTAS DE RELATÓRIOS DE FUNCIONÁRIOS =====

@empresa_principal_bp.route('/relatorios/funcionarios/geral')
@require_admin
def relatorio_funcionarios_geral():
    """Relatório geral de funcionários da empresa principal - VERSÃO MODERNIZADA"""
    try:
        empresa_principal = get_empresa_principal()
        if not empresa_principal:
            return "Empresa principal não definida", 404

        funcionarios = get_funcionarios_empresa_principal()

        # Gerar HTML do relatório com layout modernizado seguindo padrão VISUAL.md
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Relatório Geral de Funcionários</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <!-- Font Awesome -->
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <!-- PDF Export Libraries -->
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

            <style>
                /* ===== VARIÁVEIS CSS PADRÃO OFICIAL ===== */
                :root {{
                    --primary-color: #4fbdba;
                    --primary-hover: #26a69a;
                    --background-color: #f9fafb;
                    --card-background: #ffffff;
                    --text-primary: #1f2937;
                    --text-secondary: #6b7280;
                    --text-muted: #9ca3af;
                    --border-color: #e5e7eb;
                    --border-light: #f3f4f6;
                    --success-color: #10b981;
                    --success-bg: #dcfce7;
                    --success-text: #166534;
                    --danger-color: #ef4444;
                    --danger-bg: #fee2e2;
                    --danger-text: #dc2626;
                    --info-color: #3b82f6;
                    --info-bg: #dbeafe;
                    --info-text: #1e40af;
                    --warning-color: #f59e0b;
                    --warning-bg: #fef3c7;
                    --warning-text: #92400e;
                }}

                /* ===== RESET E BASE ===== */
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    font-size: 11pt;
                    line-height: 1.4;
                    background: var(--background-color);
                    color: var(--text-primary);
                    min-height: 100vh;
                    padding: 2rem;
                }}

                /* ===== CONTAINER PRINCIPAL ===== */
                .report-container {{
                    background: var(--card-background);
                    border-radius: 12px;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                    overflow: hidden;
                    max-width: 1200px;
                    margin: 0 auto;
                    border: 1px solid var(--border-color);
                }}

                /* ===== HEADER MODERNIZADO ===== */
                .report-header {{
                    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
                    color: white;
                    padding: 2rem;
                    position: relative;
                    overflow: hidden;
                }}

                .report-header::before {{
                    content: '';
                    position: absolute;
                    top: -50%;
                    right: -20%;
                    width: 60%;
                    height: 200%;
                    background: rgba(255,255,255,0.1);
                    border-radius: 50%;
                    transform: rotate(15deg);
                }}

                .header-content {{
                    position: relative;
                    z-index: 2;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    gap: 1rem;
                }}

                .header-info {{
                    flex: 1;
                    min-width: 250px;
                }}

                .report-title {{
                    font-size: 1.875rem;
                    font-weight: 700;
                    margin-bottom: 0.75rem;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }}

                .report-title i {{
                    font-size: 2rem;
                    opacity: 0.9;
                }}

                .empresa-info {{
                    font-size: 1rem;
                    margin-bottom: 0.5rem;
                    opacity: 0.95;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .data-geracao {{
                    font-size: 0.875rem;
                    opacity: 0.85;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                /* ===== BOTÕES DE AÇÃO MODERNOS ===== */
                .action-buttons {{
                    display: flex;
                    gap: 0.75rem;
                    flex-wrap: wrap;
                    align-items: flex-start;
                }}

                .btn-action {{
                    background: rgba(255,255,255,0.15);
                    border: 1px solid rgba(255,255,255,0.3);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 8px;
                    text-decoration: none;
                    font-size: 0.875rem;
                    font-weight: 500;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    backdrop-filter: blur(10px);
                }}

                .btn-action:hover {{
                    background: rgba(255,255,255,0.25);
                    color: white;
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    border-color: rgba(255,255,255,0.5);
                }}

                .btn-action i {{
                    font-size: 0.875rem;
                }}

                /* ===== CONTEÚDO DO RELATÓRIO ===== */
                .report-content {{
                    padding: 2rem;
                }}

                /* ===== CARDS DE ESTATÍSTICAS MODERNOS ===== */
                .summary-cards {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
                    gap: 1.5rem;
                    margin-bottom: 2rem;
                }}

                .summary-card {{
                    background: var(--card-background);
                    border: 1px solid var(--border-color);
                    border-radius: 12px;
                    padding: 1.5rem;
                    text-align: center;
                    position: relative;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    overflow: hidden;
                }}

                .summary-card::before {{
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 4px;
                    transition: all 0.3s ease;
                }}

                .summary-card:hover {{
                    transform: translateY(-2px);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                    border-color: var(--border-color);
                }}

                .summary-card.total::before {{
                    background: var(--info-color);
                }}

                .summary-card.active::before {{
                    background: var(--success-color);
                }}

                .summary-card.inactive::before {{
                    background: var(--danger-color);
                }}

                .summary-card .icon {{
                    font-size: 2rem;
                    margin-bottom: 0.75rem;
                    opacity: 0.7;
                }}

                .summary-card.total .icon {{
                    color: var(--info-color);
                }}

                .summary-card.active .icon {{
                    color: var(--success-color);
                }}

                .summary-card.inactive .icon {{
                    color: var(--danger-color);
                }}

                .summary-card .number {{
                    font-size: 2rem;
                    font-weight: 700;
                    color: var(--text-primary);
                    margin-bottom: 0.5rem;
                    display: block;
                }}

                .summary-card .label {{
                    font-size: 0.875rem;
                    color: var(--text-secondary);
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                }}

                .summary-card .label i {{
                    font-size: 0.75rem;
                }}

                /* ===== TABELA MODERNA ===== */
                .table-container {{
                    background: var(--card-background);
                    border: 1px solid var(--border-color);
                    border-radius: 12px;
                    overflow: hidden;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
                }}

                .modern-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0;
                    font-size: 0.875rem;
                }}

                .modern-table thead {{
                    background: var(--border-light);
                }}

                .modern-table thead th {{
                    padding: 1rem 0.75rem;
                    text-align: left;
                    font-weight: 600;
                    color: var(--text-secondary);
                    font-size: 0.75rem;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    border-bottom: 1px solid var(--border-color);
                }}

                .modern-table thead th i {{
                    margin-right: 0.5rem;
                    color: var(--text-muted);
                    font-size: 0.875rem;
                }}

                .modern-table tbody tr {{
                    border-bottom: 1px solid var(--border-color);
                    transition: background-color 0.2s ease;
                }}

                .modern-table tbody tr:hover {{
                    background: var(--border-light);
                }}

                .modern-table tbody tr:last-child {{
                    border-bottom: none;
                }}

                .modern-table tbody td {{
                    padding: 1rem 0.75rem;
                    vertical-align: middle;
                    color: var(--text-primary);
                }}

                .modern-table tbody td:first-child {{
                    font-weight: 600;
                }}

                /* ===== BADGES DE STATUS MODERNOS ===== */
                .status-badge {{
                    display: inline-flex;
                    align-items: center;
                    padding: 0.25rem 0.75rem;
                    border-radius: 20px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.025em;
                }}

                .status-ativo {{
                    background: var(--success-bg);
                    color: var(--success-text);
                    border: 1px solid var(--success-color);
                }}

                .status-inativo {{
                    background: var(--danger-bg);
                    color: var(--danger-text);
                    border: 1px solid var(--danger-color);
                }}

                .matricula-badge {{
                    background: var(--info-bg);
                    color: var(--info-text);
                    padding: 0.25rem 0.5rem;
                    border-radius: 6px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    font-family: monospace;
                }}

                /* ===== ESTADO VAZIO ===== */
                .no-data {{
                    text-align: center;
                    padding: 3rem 1rem;
                    color: var(--text-muted);
                }}

                .no-data i {{
                    font-size: 3rem;
                    margin-bottom: 1rem;
                    color: var(--text-muted);
                    opacity: 0.5;
                }}

                .no-data h3 {{
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: var(--text-secondary);
                    margin-bottom: 0.5rem;
                }}

                .no-data p {{
                    font-size: 0.875rem;
                    color: var(--text-muted);
                }}

                /* ===== RODAPÉ MINIMALISTA ===== */
                .footer {{
                    background: var(--border-light);
                    padding: 1rem 2rem;
                    text-align: center;
                    color: var(--text-muted);
                    font-size: 0.875rem;
                    border-top: 1px solid var(--border-color);
                }}

                .footer i {{
                    margin-right: 0.5rem;
                    color: var(--primary-color);
                }}

                /* ===== ANIMAÇÕES SUAVES ===== */
                @keyframes fadeInUp {{
                    from {{
                        opacity: 0;
                        transform: translateY(20px);
                    }}
                    to {{
                        opacity: 1;
                        transform: translateY(0);
                    }}
                }}

                .summary-card {{
                    animation: fadeInUp 0.5s ease-out;
                }}

                .summary-card:nth-child(1) {{ animation-delay: 0.1s; opacity: 0; animation-fill-mode: forwards; }}
                .summary-card:nth-child(2) {{ animation-delay: 0.2s; opacity: 0; animation-fill-mode: forwards; }}
                .summary-card:nth-child(3) {{ animation-delay: 0.3s; opacity: 0; animation-fill-mode: forwards; }}

                /* ===== RESPONSIVIDADE ===== */
                @media (max-width: 768px) {{
                    body {{
                        padding: 1rem;
                    }}

                    .report-header {{
                        padding: 1.5rem;
                    }}

                    .header-content {{
                        flex-direction: column;
                        align-items: center;
                        text-align: center;
                        gap: 1.5rem;
                    }}

                    .action-buttons {{
                        justify-content: center;
                    }}

                    .report-content {{
                        padding: 1.5rem;
                    }}

                    .summary-cards {{
                        grid-template-columns: 1fr;
                        gap: 1rem;
                    }}

                    .modern-table {{
                        font-size: 0.75rem;
                    }}

                    .modern-table thead th,
                    .modern-table tbody td {{
                        padding: 0.75rem 0.5rem;
                    }}

                    .report-title {{
                        font-size: 1.5rem;
                    }}

                    .report-title i {{
                        font-size: 1.5rem;
                    }}
                }}

                @media (max-width: 480px) {{
                    .summary-cards {{
                        grid-template-columns: 1fr;
                    }}

                    .action-buttons {{
                        flex-direction: column;
                        align-items: stretch;
                    }}

                    .btn-action {{
                        justify-content: center;
                    }}

                    .table-container {{
                        overflow-x: auto;
                    }}

                    .modern-table {{
                        min-width: 500px;
                    }}
                }}

                /* ===== ESTILOS DE IMPRESSÃO ===== */
                @media print {{
                    body {{
                        background: white !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        font-size: 9pt !important;
                    }}

                    .action-buttons {{
                        display: none !important;
                    }}

                    .report-container {{
                        box-shadow: none !important;
                        border-radius: 0 !important;
                        margin: 0 !important;
                        border: none !important;
                    }}

                    .report-header {{
                        background: white !important;
                        color: #333 !important;
                        padding: 15px !important;
                        border-bottom: 2px solid #333 !important;
                    }}

                    .report-header::before {{
                        display: none !important;
                    }}

                    .report-title {{
                        color: #333 !important;
                        font-size: 14pt !important;
                    }}

                    .empresa-info,
                    .data-geracao {{
                        color: #666 !important;
                        font-size: 10pt !important;
                    }}

                    .summary-cards {{
                        display: none !important;
                    }}

                    .report-content {{
                        padding: 15px !important;
                    }}

                    .table-container {{
                        box-shadow: none !important;
                        border: 1px solid #333 !important;
                    }}

                    .modern-table thead th {{
                        background: #f8f9fa !important;
                        color: #333 !important;
                        border: 1px solid #333 !important;
                        font-size: 8pt !important;
                        padding: 6px !important;
                    }}

                    .modern-table tbody td {{
                        border: 1px solid #333 !important;
                        padding: 6px !important;
                        font-size: 8pt !important;
                    }}

                    .footer {{
                        background: white !important;
                        border-top: 1px solid #333 !important;
                        padding: 8px !important;
                        font-size: 8pt !important;
                    }}

                    .status-badge {{
                        background: none !important;
                        border: 1px solid #333 !important;
                        color: #333 !important;
                    }}

                    .matricula-badge {{
                        background: none !important;
                        border: 1px solid #333 !important;
                        color: #333 !important;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="report-container" id="reportContent">
                <!-- Header Modernizado -->
                <div class="report-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="report-title">
                                <i class="fas fa-users"></i>
                                Relatório Geral de Funcionários
                            </h1>
                            <div class="empresa-info">
                                <i class="fas fa-building"></i>
                                {empresa_principal.get('nome_fantasia', empresa_principal['razao_social'])}
                            </div>
                            <div class="empresa-info">
                                <i class="fas fa-id-card"></i>
                                CNPJ: {empresa_principal.get('cnpj', 'Não informado')}
                            </div>
                            <div class="data-geracao">
                                <i class="fas fa-calendar-alt"></i>
                                Gerado em: {datetime.now().strftime('%d/%m/%Y às %H:%M')}
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn-action" onclick="voltarPagina()" title="Voltar à página anterior">
                                <i class="fas fa-arrow-left"></i>
                                Voltar
                            </button>
                            <button class="btn-action" onclick="window.print()" title="Imprimir Relatório">
                                <i class="fas fa-print"></i>
                                Imprimir
                            </button>
                            <button class="btn-action" onclick="exportToPDF()" title="Exportar como PDF">
                                <i class="fas fa-file-pdf"></i>
                                PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Conteúdo do Relatório -->
                <div class="report-content">
                    <!-- Cards de Estatísticas Modernos -->
                    <div class="summary-cards">
                        <div class="summary-card total">
                            <div class="icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <span class="number">{len(funcionarios) if funcionarios else 0}</span>
                            <div class="label">
                                <i class="fas fa-chart-bar"></i>
                                Total de Funcionários
                            </div>
                        </div>

                        <div class="summary-card active">
                            <div class="icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <span class="number">{len([f for f in funcionarios if f.get('status_cadastro') == 'Ativo']) if funcionarios else 0}</span>
                            <div class="label">
                                <i class="fas fa-circle"></i>
                                Funcionários Ativos
                            </div>
                        </div>

                        <div class="summary-card inactive">
                            <div class="icon">
                                <i class="fas fa-user-times"></i>
                            </div>
                            <span class="number">{len([f for f in funcionarios if f.get('status_cadastro') != 'Ativo']) if funcionarios else 0}</span>
                            <div class="label">
                                <i class="fas fa-circle"></i>
                                Funcionários Inativos
                            </div>
                        </div>
                    </div>

                    <!-- Tabela Moderna -->
                    <div class="table-container">
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-user"></i> Nome</th>
                                    <th><i class="fas fa-id-card"></i> CPF</th>
                                    <th><i class="fas fa-hashtag"></i> Matrícula</th>
                                    <th><i class="fas fa-briefcase"></i> Cargo</th>
                                    <th><i class="fas fa-calendar-plus"></i> Data Admissão</th>
                                    <th><i class="fas fa-toggle-on"></i> Status</th>
                                </tr>
                            </thead>
                            <tbody>
        """

        if funcionarios:
            for func in funcionarios:
                status_class = "status-ativo" if func.get('status_cadastro') == 'Ativo' else "status-inativo"
                html_content += f"""
                                <tr>
                                    <td>{func.get('nome_completo', 'N/A')}</td>
                                    <td>{func.get('cpf', 'N/A')}</td>
                                    <td><span class="matricula-badge">{func.get('matricula', 'N/A')}</span></td>
                                    <td>{func.get('cargo', 'N/A')}</td>
                                    <td>{func.get('data_admissao_formatada', 'N/A')}</td>
                                    <td><span class="status-badge {status_class}">{func.get('status_display', 'N/A')}</span></td>
                                </tr>
                """
        else:
            html_content += '''
                                <tr>
                                    <td colspan="6">
                                        <div class="no-data">
                                            <i class="fas fa-inbox"></i>
                                            <h3>Nenhum funcionário encontrado</h3>
                                            <p>Não há funcionários cadastrados na empresa principal</p>
                                        </div>
                                    </td>
                                </tr>
            '''

        html_content += f"""
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Rodapé Minimalista -->
                <div class="footer">
                    <p>
                        <i class="fas fa-shield-alt"></i>
                        Sistema de Controle de Ponto - {empresa_principal.get('nome_fantasia', empresa_principal['razao_social'])}
                    </p>
                </div>
            </div>

            <script>
                function exportToPDF() {{
                    // Aplicar estilos de impressão temporariamente
                    const originalDisplay = document.querySelector('.action-buttons').style.display;
                    const originalCardsDisplay = document.querySelector('.summary-cards').style.display;
                    
                    document.querySelector('.action-buttons').style.display = 'none';
                    document.querySelector('.summary-cards').style.display = 'none';

                    const element = document.getElementById('reportContent');
                    const opt = {{
                        margin: [0.3, 0.3, 0.3, 0.3],
                        filename: 'relatorio-funcionarios-{empresa_principal.get("nome_fantasia", "empresa").replace(" ", "-").lower()}-{datetime.now().strftime("%Y%m%d_%H%M")}.pdf',
                        image: {{
                            type: 'jpeg',
                            quality: 0.95,
                            format: 'a4'
                        }},
                        html2canvas: {{
                            scale: 2,
                            useCORS: true,
                            letterRendering: true,
                            backgroundColor: '#ffffff',
                            logging: false,
                            allowTaint: false,
                            removeContainer: true
                        }},
                        jsPDF: {{
                            unit: 'mm',
                            format: 'a4',
                            orientation: 'portrait',
                            compress: true
                        }},
                        pagebreak: {{ mode: ['avoid-all', 'css', 'legacy'] }}
                    }};

                    if (typeof html2pdf !== 'undefined') {{
                        html2pdf().set(opt).from(element).save().then(() => {{
                            document.querySelector('.action-buttons').style.display = originalDisplay;
                            document.querySelector('.summary-cards').style.display = originalCardsDisplay;
                        }});
                    }} else {{
                        // Fallback para jsPDF básico
                        html2canvas(element, {{
                            scale: 2,
                            useCORS: true,
                            backgroundColor: '#ffffff'
                        }}).then(canvas => {{
                            const imgData = canvas.toDataURL('image/png');
                            const {{ jsPDF }} = window.jspdf;
                            const pdf = new jsPDF('p', 'mm', 'a4');

                            const imgWidth = 210;
                            const pageHeight = 295;
                            const imgHeight = (canvas.height * imgWidth) / canvas.width;
                            let heightLeft = imgHeight;
                            let position = 0;

                            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                            heightLeft -= pageHeight;

                            while (heightLeft >= 0) {{
                                position = heightLeft - imgHeight;
                                pdf.addPage();
                                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                                heightLeft -= pageHeight;
                            }}

                            pdf.save('relatorio-funcionarios-{datetime.now().strftime("%Y%m%d_%H%M")}.pdf');
                            document.querySelector('.action-buttons').style.display = originalDisplay;
                            document.querySelector('.summary-cards').style.display = originalCardsDisplay;
                        }});
                    }}
                }}

                function voltarPagina() {{
                    if (window.history.length > 1) {{
                        window.history.back();
                    }} else {{
                        window.location.href = '/empresa-principal/funcionarios';
                    }}
                }}

                // Animações de entrada
                document.addEventListener('DOMContentLoaded', function() {{
                    const cards = document.querySelectorAll('.summary-card');
                    cards.forEach((card, index) => {{
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';
                        
                        setTimeout(() => {{
                            card.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }}, 100 + (index * 100));
                    }});

                    // Animação da tabela
                    const table = document.querySelector('.table-container');
                    if (table) {{
                        setTimeout(() => {{
                            table.style.opacity = '0';
                            table.style.transform = 'translateY(20px)';
                            table.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                            
                            setTimeout(() => {{
                                table.style.opacity = '1';
                                table.style.transform = 'translateY(0)';
                            }}, 50);
                        }}, 400);
                    }}
                }});
            </script>
        </body>
        </html>
        """

        return html_content

    except Exception as e:
        logger.error(f"Erro ao gerar relatório geral de funcionários: {e}")
        return f"Erro ao gerar relatório: {str(e)}", 500

@empresa_principal_bp.route('/relatorios/funcionarios/frequencia')
@require_admin
def relatorio_funcionarios_frequencia():
    """Relatório de frequência geral dos funcionários"""
    try:
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Relatório de Frequência</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; text-align: center; }
                .em-desenvolvimento { color: #666; margin-top: 50px; }
            </style>
        </head>
        <body>
            <h1>Relatório de Frequência Geral</h1>
            <div class="em-desenvolvimento">
                <h3>🚧 Em Desenvolvimento</h3>
                <p>Esta funcionalidade será implementada em breve.</p>
                <p>Incluirá dados de presença, faltas, atrasos e horas extras.</p>
            </div>
        </body>
        </html>
        """

    except Exception as e:
        logger.error(f"Erro ao gerar relatório de frequência: {e}")
        return f"Erro ao gerar relatório: {str(e)}", 500



@empresa_principal_bp.route('/relatorios/funcionarios/exportar')
@require_admin
def exportar_funcionarios():
    """Exportar lista de funcionários em CSV"""
    try:
        funcionarios = get_funcionarios_empresa_principal()

        # Criar CSV
        import csv
        from io import StringIO

        output = StringIO()
        writer = csv.writer(output)

        # Cabeçalho
        writer.writerow(['Nome', 'CPF', 'Matrícula', 'Cargo', 'Data Admissão', 'Status', 'Telefone'])

        # Dados
        if funcionarios:
            for func in funcionarios:
                writer.writerow([
                    func.get('nome_completo', ''),
                    func.get('cpf', ''),
                    func.get('matricula', ''),
                    func.get('cargo', ''),
                    func.get('data_admissao_formatada', ''),
                    func.get('status_display', ''),
                    func.get('telefone1', '')
                ])

        output.seek(0)

        from flask import Response
        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': 'attachment; filename=funcionarios_empresa_principal.csv'}
        )

    except Exception as e:
        logger.error(f"Erro ao exportar funcionários: {e}")
        return f"Erro ao exportar: {str(e)}", 500

@empresa_principal_bp.route('/relatorios/funcionario/<int:funcionario_id>')
@require_admin
def relatorio_funcionario_individual(funcionario_id):
    """Relatório individual de um funcionário"""
    try:
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Relatório Individual</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; text-align: center; }}
                .em-desenvolvimento {{ color: #666; margin-top: 50px; }}
            </style>
        </head>
        <body>
            <h1>Relatório Individual - Funcionário ID: {funcionario_id}</h1>
            <div class="em-desenvolvimento">
                <h3>🚧 Em Desenvolvimento</h3>
                <p>Esta funcionalidade será implementada em breve.</p>
                <p>Incluirá histórico completo do funcionário, registros de ponto, faltas, etc.</p>
            </div>
        </body>
        </html>
        """

    except Exception as e:
        logger.error(f"Erro ao gerar relatório individual: {e}")
        return f"Erro ao gerar relatório: {str(e)}", 500

# ================================================================
# REGISTRAR BLUEPRINT
# ================================================================

def register_empresa_principal_routes(app):
    """Registrar rotas da empresa principal"""
    app.register_blueprint(empresa_principal_bp)
    logger.info("Rotas da empresa principal registradas")

if __name__ == '__main__':
    print("Módulo de Empresa Principal carregado")
