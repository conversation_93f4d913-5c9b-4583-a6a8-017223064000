#!/usr/bin/env python3
"""
Script para investigar as jornadas no sistema RLPONTO-WEB
"""

import sys
import os
sys.path.append('/var/www/controle-ponto')

from utils.database import DatabaseManager

def investigar_jornadas():
    """Investigar configurações de jornadas"""
    try:
        db = DatabaseManager()
        
        print("🔍 INVESTIGAÇÃO: JORNADAS NO SISTEMA")
        print("=" * 60)
        
        # 1. Verificar empresas
        print("\n1. EMPRESAS NO SISTEMA:")
        empresas_query = """
        SELECT id, razao_social, nome_fantasia, empresa_principal, ativa
        FROM empresas 
        WHERE ativa = 1
        ORDER BY empresa_principal DESC, nome_fantasia
        """
        
        empresas = db.execute_query(empresas_query)
        
        if empresas:
            for emp in empresas:
                tipo = "PRINCIPAL" if emp['empresa_principal'] else "CLIENTE"
                print(f"   ID: {emp['id']} | {emp['nome_fantasia']} | {tipo}")
        else:
            print("   ❌ Nenhuma empresa encontrada!")
            return
        
        # 2. Verificar jornadas por empresa
        print("\n2. JORNADAS POR EMPRESA:")
        for emp in empresas:
            print(f"\n   📋 {emp['nome_fantasia']} (ID: {emp['id']}):")
            
            jornadas_query = """
            SELECT id, nome_jornada, padrao, ativa, 
                   seg_qui_entrada, seg_qui_saida,
                   sexta_entrada, sexta_saida
            FROM jornadas_trabalho 
            WHERE empresa_id = %s AND ativa = 1
            ORDER BY padrao DESC, nome_jornada
            """
            
            jornadas = db.execute_query(jornadas_query, (emp['id'],))
            
            if jornadas:
                for jornada in jornadas:
                    padrao_txt = " (PADRÃO)" if jornada['padrao'] else ""
                    print(f"      ✅ ID: {jornada['id']} | {jornada['nome_jornada']}{padrao_txt}")
                    print(f"         Seg-Qui: {jornada['seg_qui_entrada']} às {jornada['seg_qui_saida']}")
                    print(f"         Sexta: {jornada['sexta_entrada']} às {jornada['sexta_saida']}")
            else:
                print("      ❌ Sem jornadas configuradas!")
        
        # 3. Verificar funcionários e suas jornadas
        print("\n3. FUNCIONÁRIOS E JORNADAS:")
        funcionarios_query = """
        SELECT f.id, f.nome_completo, f.empresa_id, f.jornada_trabalho_id,
               e.nome_fantasia as empresa_nome,
               jt.nome_jornada
        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        WHERE f.status_cadastro = 'Ativo'
        ORDER BY e.nome_fantasia, f.nome_completo
        """
        
        funcionarios = db.execute_query(funcionarios_query)
        
        if funcionarios:
            empresa_atual = None
            for func in funcionarios:
                if empresa_atual != func['empresa_nome']:
                    empresa_atual = func['empresa_nome']
                    print(f"\n   📋 {empresa_atual}:")
                
                jornada_info = func.get('jornada_nome', "❌ SEM JORNADA") or "❌ SEM JORNADA"
                print(f"      👤 {func['nome_completo']} | Jornada: {jornada_info}")
        else:
            print("   ❌ Nenhum funcionário encontrado!")
        
        # 4. Verificar alocações
        print("\n4. ALOCAÇÕES ATIVAS:")
        alocacoes_query = """
        SELECT fa.funcionario_id, fa.empresa_cliente_id, fa.jornada_trabalho_id,
               f.nome_completo, 
               ec.nome_fantasia as cliente_nome,
               jt.nome_jornada as jornada_alocacao
        FROM funcionario_alocacoes fa
        JOIN funcionarios f ON fa.funcionario_id = f.id
        JOIN empresas ec ON fa.empresa_cliente_id = ec.id
        LEFT JOIN jornadas_trabalho jt ON fa.jornada_trabalho_id = jt.id
        WHERE fa.ativo = 1
        ORDER BY ec.nome_fantasia, f.nome_completo
        """
        
        alocacoes = db.execute_query(alocacoes_query)
        
        if alocacoes:
            cliente_atual = None
            for aloc in alocacoes:
                if cliente_atual != aloc['cliente_nome']:
                    cliente_atual = aloc['cliente_nome']
                    print(f"\n   🏢 {cliente_atual}:")
                
                jornada_info = aloc['jornada_alocacao'] if aloc['jornada_alocacao'] else "❌ SEM JORNADA"
                print(f"      👤 {aloc['nome_completo']} | Jornada Alocação: {jornada_info}")
        else:
            print("   ❌ Nenhuma alocação encontrada!")
        
        # 5. Verificar tabela horarios_trabalho (sistema antigo?)
        print("\n5. HORÁRIOS DE TRABALHO (SISTEMA ANTIGO?):")
        horarios_query = """
        SELECT ht.id, ht.nome_horario, ht.empresa_id, ht.ativo,
               e.nome_fantasia,
               ht.entrada_manha, ht.saida_almoco, ht.entrada_tarde, ht.saida
        FROM horarios_trabalho ht
        LEFT JOIN empresas e ON ht.empresa_id = e.id
        WHERE ht.ativo = 1
        ORDER BY e.nome_fantasia, ht.nome_horario
        """
        
        horarios = db.execute_query(horarios_query)
        
        if horarios:
            empresa_atual = None
            for horario in horarios:
                if empresa_atual != horario['nome_fantasia']:
                    empresa_atual = horario['nome_fantasia']
                    print(f"\n   📋 {empresa_atual}:")
                
                print(f"      ⏰ {horario['nome_horario']}")
                print(f"         Manhã: {horario['entrada_manha']} às {horario['saida_almoco']}")
                print(f"         Tarde: {horario['entrada_tarde']} às {horario['saida']}")
        else:
            print("   ✅ Nenhum horário antigo encontrado!")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigar_jornadas()
