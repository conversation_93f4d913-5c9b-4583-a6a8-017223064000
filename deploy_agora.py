#!/usr/bin/env python3
"""
Deploy Imediato - Correção JavaScript RLPONTO-WEB
Data: 03/07/2025
Executa deploy real no servidor ************
"""

import os
import subprocess
import sys
from datetime import datetime

# Configurações do servidor
SERVER = "************"
USER = "root"
PASSWORD = "@Ric6109"
REMOTE_PATH = "/var/www/controle-ponto/templates/configuracoes/"
LOCAL_FILE = "var/www/controle-ponto/templates/configuracoes/empresa_form.html"

def log(message, level="INFO"):
    """Log com timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def check_file_exists():
    """Verificar se o arquivo local existe"""
    if not os.path.exists(LOCAL_FILE):
        log(f"ERRO: Arquivo local não encontrado: {LOCAL_FILE}", "ERROR")
        return False
    
    log(f"Arquivo local encontrado: {LOCAL_FILE}", "SUCCESS")
    return True

def create_ssh_commands():
    """Criar comandos SSH para executar no servidor"""
    commands = f"""
echo "=== DEPLOY CORREÇÃO JAVASCRIPT EMPRESAS ==="
echo "Data: $(date)"
echo "Servidor: {SERVER}"
echo ""

echo "📁 Navegando para o diretório..."
cd {REMOTE_PATH}
pwd

echo ""
echo "💾 Criando backup do arquivo atual..."
cp empresa_form.html empresa_form_backup_$(date +%Y%m%d_%H%M%S).html
echo "✅ Backup criado"

echo ""
echo "📋 Listando arquivos atuais:"
ls -la empresa_form*

echo ""
echo "🔍 Verificando status do serviço..."
systemctl is-active rlponto-web 2>/dev/null || echo "Serviço não encontrado via systemctl"
ps aux | grep python | grep app.py | head -3

echo ""
echo "⏳ Servidor preparado para receber arquivo corrigido..."
echo "Execute: scp '{LOCAL_FILE}' {USER}@{SERVER}:{REMOTE_PATH}"
"""
    return commands

def execute_ssh_preparation():
    """Executar preparação via SSH"""
    log("Conectando ao servidor para preparar deploy...", "INFO")
    
    commands = create_ssh_commands()
    
    try:
        # Executar comandos SSH
        ssh_cmd = f'ssh -o StrictHostKeyChecking=no {USER}@{SERVER} "{commands}"'
        log(f"Executando: {ssh_cmd}", "DEBUG")
        
        result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            log("Preparação do servidor concluída!", "SUCCESS")
            print(result.stdout)
            return True
        else:
            log(f"Erro na preparação: {result.stderr}", "ERROR")
            return False
            
    except subprocess.TimeoutExpired:
        log("Timeout na conexão SSH", "ERROR")
        return False
    except Exception as e:
        log(f"Erro na execução SSH: {e}", "ERROR")
        return False

def execute_scp_deploy():
    """Executar deploy via SCP"""
    log("Iniciando transferência do arquivo via SCP...", "INFO")
    
    try:
        scp_cmd = f'scp "{LOCAL_FILE}" {USER}@{SERVER}:{REMOTE_PATH}'
        log(f"Executando: {scp_cmd}", "DEBUG")
        
        result = subprocess.run(scp_cmd, shell=True, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            log("Arquivo transferido com sucesso!", "SUCCESS")
            return True
        else:
            log(f"Erro na transferência: {result.stderr}", "ERROR")
            return False
            
    except subprocess.TimeoutExpired:
        log("Timeout na transferência SCP", "ERROR")
        return False
    except Exception as e:
        log(f"Erro na transferência: {e}", "ERROR")
        return False

def finalize_deploy():
    """Finalizar deploy no servidor"""
    log("Finalizando deploy no servidor...", "INFO")
    
    commands = f"""
echo "🔧 Finalizando deploy..."
cd {REMOTE_PATH}

echo "📋 Verificando arquivo transferido:"
ls -la empresa_form.html
stat empresa_form.html

echo ""
echo "🔐 Ajustando permissões..."
chown www-data:www-data empresa_form.html 2>/dev/null || chown apache:apache empresa_form.html 2>/dev/null || echo "Permissões mantidas"
chmod 644 empresa_form.html

echo ""
echo "🔄 Reiniciando serviço..."
systemctl restart rlponto-web 2>/dev/null || echo "Reinício via systemctl não disponível"

echo ""
echo "🧪 Testando aplicação..."
curl -I http://localhost:5000 2>/dev/null || curl -I http://127.0.0.1:5000 2>/dev/null || echo "Teste de conectividade local não disponível"

echo ""
echo "✅ Deploy finalizado!"
echo "🎯 Teste agora: http://{SERVER}:5000"
"""
    
    try:
        ssh_cmd = f'ssh -o StrictHostKeyChecking=no {USER}@{SERVER} "{commands}"'
        result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            log("Deploy finalizado com sucesso!", "SUCCESS")
            print(result.stdout)
            return True
        else:
            log(f"Erro na finalização: {result.stderr}", "ERROR")
            return False
            
    except Exception as e:
        log(f"Erro na finalização: {e}", "ERROR")
        return False

def main():
    """Função principal do deploy"""
    log("=== DEPLOY IMEDIATO - CORREÇÃO JAVASCRIPT ===", "INFO")
    log(f"Servidor: {SERVER}", "INFO")
    log(f"Arquivo: {LOCAL_FILE}", "INFO")
    print()
    
    # Verificar arquivo local
    if not check_file_exists():
        sys.exit(1)
    
    # Preparar servidor
    log("ETAPA 1: Preparando servidor...", "INFO")
    if not execute_ssh_preparation():
        log("Falha na preparação. Continuando com deploy manual...", "WARNING")
    
    # Transferir arquivo
    log("ETAPA 2: Transferindo arquivo...", "INFO")
    if not execute_scp_deploy():
        log("Falha na transferência automática. Deploy manual necessário.", "ERROR")
        print_manual_instructions()
        return
    
    # Finalizar deploy
    log("ETAPA 3: Finalizando deploy...", "INFO")
    if not finalize_deploy():
        log("Falha na finalização. Verificação manual necessária.", "WARNING")
    
    # Instruções finais
    print()
    log("🎉 DEPLOY CONCLUÍDO!", "SUCCESS")
    log("🧪 TESTE AGORA:", "INFO")
    log(f"   1. Acesse: http://{SERVER}:5000", "INFO")
    log("   2. Login: admin / @Ric6109", "INFO")
    log("   3. Vá para: Configurações > Empresas", "INFO")
    log("   4. Edite uma empresa e salve", "INFO")
    log("   5. Verifique se o erro JavaScript foi corrigido!", "INFO")

def print_manual_instructions():
    """Imprimir instruções manuais"""
    print()
    log("📋 INSTRUÇÕES PARA DEPLOY MANUAL:", "WARNING")
    print(f"""
1. Conectar ao servidor:
   ssh {USER}@{SERVER}
   Senha: {PASSWORD}

2. Navegar e fazer backup:
   cd {REMOTE_PATH}
   cp empresa_form.html empresa_form_backup_$(date +%Y%m%d_%H%M%S).html

3. Copiar arquivo (use WinSCP, FileZilla ou SCP):
   De: {os.path.abspath(LOCAL_FILE)}
   Para: {USER}@{SERVER}:{REMOTE_PATH}

4. Ajustar permissões:
   chown www-data:www-data empresa_form.html
   chmod 644 empresa_form.html

5. Reiniciar serviço:
   systemctl restart rlponto-web
""")

if __name__ == "__main__":
    main()
