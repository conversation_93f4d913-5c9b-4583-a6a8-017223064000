#!/usr/bin/env python3
import paramiko
import os

def deploy_files():
    # Configurações do servidor
    hostname = '************'
    username = 'root'
    password = '@Ric6109'
    
    try:
        # Conectar via SSH
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        
        print("Conectado ao servidor com sucesso!")
        
        # Conteúdo do arquivo empresa.py
        empresa_content = '''from database import get_db_connection
import logging

logger = logging.getLogger(__name__)

class Empresa:
    def __init__(self, id=None, nome_empresa=None, logotipo=None, logotipo_mime_type=None, 
                 regras_especificas=None, tolerancia_atraso=10, tolerancia_saida_antecipada=10,
                 jornada_trabalho_padrao='08:00:00', intervalo_almoco_inicio='12:00:00',
                 intervalo_almoco_fim='13:00:00', data_criacao=None, data_atualizacao=None, ativa=True):
        self.id = id
        self.nome_empresa = nome_empresa
        self.logotipo = logotipo
        self.logotipo_mime_type = logotipo_mime_type
        self.regras_especificas = regras_especificas
        self.tolerancia_atraso = tolerancia_atraso
        self.tolerancia_saida_antecipada = tolerancia_saida_antecipada
        self.jornada_trabalho_padrao = jornada_trabalho_padrao
        self.intervalo_almoco_inicio = intervalo_almoco_inicio
        self.intervalo_almoco_fim = intervalo_almoco_fim
        self.data_criacao = data_criacao
        self.data_atualizacao = data_atualizacao
        self.ativa = ativa

    @classmethod
    def criar(cls, nome_empresa, logotipo=None, logotipo_mime_type=None, regras_especificas=None,
              tolerancia_atraso=10, tolerancia_saida_antecipada=10, jornada_trabalho_padrao='08:00:00',
              intervalo_almoco_inicio='12:00:00', intervalo_almoco_fim='13:00:00'):
        """Cria uma nova empresa no banco de dados"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = """
                INSERT INTO cad_empresas
                (nome_empresa, logotipo, logotipo_mime_type, regras_especificas,
                 tolerancia_atraso, tolerancia_saida_antecipada, jornada_trabalho_padrao,
                 intervalo_almoco_inicio, intervalo_almoco_fim)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(query, (
                nome_empresa, logotipo, logotipo_mime_type, regras_especificas,
                tolerancia_atraso, tolerancia_saida_antecipada, jornada_trabalho_padrao,
                intervalo_almoco_inicio, intervalo_almoco_fim
            ))
            
            empresa_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Empresa '{nome_empresa}' criada com ID {empresa_id}")
            return cls.buscar_por_id(empresa_id)
            
        except Exception as e:
            logger.error(f"Erro ao criar empresa: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

    @classmethod
    def buscar_por_id(cls, empresa_id):
        """Busca uma empresa pelo ID"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            cursor.execute("SELECT * FROM cad_empresas WHERE id = %s", (empresa_id,))
            resultado = cursor.fetchone()
            
            if resultado:
                return cls(**resultado)
            return None
            
        except Exception as e:
            logger.error(f"Erro ao buscar empresa por ID {empresa_id}: {e}")
            raise
        finally:
            if conn:
                conn.close()

    @classmethod
    def listar_todas(cls, ativas_apenas=True):
        """Lista todas as empresas"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            if ativas_apenas:
                cursor.execute("SELECT * FROM cad_empresas WHERE ativa = 1 ORDER BY nome_empresa")
            else:
                cursor.execute("SELECT * FROM cad_empresas ORDER BY nome_empresa")
            
            resultados = cursor.fetchall()
            return [cls(**resultado) for resultado in resultados]
            
        except Exception as e:
            logger.error(f"Erro ao listar empresas: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def atualizar(self):
        """Atualiza os dados da empresa no banco"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = """
                UPDATE cad_empresas SET
                nome_empresa = %s, logotipo = %s, logotipo_mime_type = %s,
                regras_especificas = %s, tolerancia_atraso = %s, tolerancia_saida_antecipada = %s,
                jornada_trabalho_padrao = %s, intervalo_almoco_inicio = %s, intervalo_almoco_fim = %s,
                ativa = %s
                WHERE id = %s
            """
            
            cursor.execute(query, (
                self.nome_empresa, self.logotipo, self.logotipo_mime_type,
                self.regras_especificas, self.tolerancia_atraso, self.tolerancia_saida_antecipada,
                self.jornada_trabalho_padrao, self.intervalo_almoco_inicio, self.intervalo_almoco_fim,
                self.ativa, self.id
            ))
            
            conn.commit()
            logger.info(f"Empresa ID {self.id} atualizada com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa ID {self.id}: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

    def excluir(self):
        """Exclui logicamente a empresa (marca como inativa)"""
        self.ativa = False
        self.atualizar()
        logger.info(f"Empresa ID {self.id} marcada como inativa")

    def to_dict(self):
        """Converte a empresa para dicionário"""
        return {
            'id': self.id,
            'nome_empresa': self.nome_empresa,
            'logotipo': self.logotipo,
            'logotipo_mime_type': self.logotipo_mime_type,
            'regras_especificas': self.regras_especificas,
            'tolerancia_atraso': self.tolerancia_atraso,
            'tolerancia_saida_antecipada': self.tolerancia_saida_antecipada,
            'jornada_trabalho_padrao': str(self.jornada_trabalho_padrao) if self.jornada_trabalho_padrao else None,
            'intervalo_almoco_inicio': str(self.intervalo_almoco_inicio) if self.intervalo_almoco_inicio else None,
            'intervalo_almoco_fim': str(self.intervalo_almoco_fim) if self.intervalo_almoco_fim else None,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None,
            'ativa': self.ativa
        }
'''
        
        # Verificar estrutura do servidor
        print("Verificando estrutura do servidor...")
        stdin, stdout, stderr = ssh.exec_command('ls -la /var/www/controle-ponto/')
        structure = stdout.read().decode()
        print("Estrutura encontrada:")
        print(structure)

        # Criar diretório models se não existir
        print("Criando diretório models...")
        stdin, stdout, stderr = ssh.exec_command('mkdir -p /var/www/controle-ponto/models')

        # Criar arquivo no servidor
        print("Criando arquivo empresa.py...")
        stdin, stdout, stderr = ssh.exec_command(f'cat > /var/www/controle-ponto/models/empresa.py << "EOF"\n{empresa_content}\nEOF')
        
        # Verificar se houve erro
        error = stderr.read().decode()
        if error:
            print(f"Erro: {error}")
        else:
            print("Arquivo empresa.py criado com sucesso!")

        # Verificar se o arquivo foi criado
        print("Verificando se o arquivo foi criado...")
        stdin, stdout, stderr = ssh.exec_command('ls -la /var/www/controle-ponto/models/')
        files = stdout.read().decode()
        print("Arquivos no diretório models:")
        print(files)

        # Reiniciar o serviço
        print("Reiniciando serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl restart controle-ponto')
        
        # Verificar status
        print("Verificando status do serviço...")
        stdin, stdout, stderr = ssh.exec_command('systemctl status controle-ponto --no-pager')
        status = stdout.read().decode()
        print(status)
        
        ssh.close()
        print("Deploy concluído com sucesso!")
        
    except Exception as e:
        print(f"Erro durante o deploy: {e}")

if __name__ == "__main__":
    deploy_files()
