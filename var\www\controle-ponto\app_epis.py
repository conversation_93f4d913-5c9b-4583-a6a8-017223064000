#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Módulo de EPIs - Controle de Ponto
----------------------------------

<PERSON>ste módulo contém todas as rotas e funcionalidades relacionadas
ao gerenciamento de EPIs dos funcionários.
"""

import logging
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from utils.database import DatabaseManager
from utils.auth import require_login, require_admin, get_current_user, get_template_context
from utils.helpers import format_date, generate_breadcrumbs, safe_int

# Configuração do logger
logger = logging.getLogger('controle-ponto.epis')

# Criação do Blueprint
epis_bp = Blueprint('epis', __name__, url_prefix='/epis')

@epis_bp.route('/funcionario/<int:funcionario_id>')
@require_login
def listar_epis(funcionario_id):
    """
    Lista EPIs de um funcionário específico.
    """
    try:
        # Buscar funcionário
        funcionario = DatabaseManager.execute_query(
            "SELECT nome_completo FROM funcionarios WHERE id = %s",
            (funcionario_id,),
            fetch_one=True
        )
        
        if not funcionario:
            flash("Funcionário não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        # Buscar EPIs
        epis = DatabaseManager.execute_query(
            "SELECT * FROM epis WHERE funcionario_id = %s ORDER BY epi_data_entrega DESC",
            (funcionario_id,)
        )
        
        # Adicionar data atual e processamento de validade
        from datetime import date, timedelta
        current_date = date.today()
        alert_date = current_date + timedelta(days=30)
        
        # Processar cada EPI para adicionar informações de validade
        for epi in epis:
            if epi['epi_data_validade']:
                if epi['epi_data_validade'] < current_date:
                    epi['status_validade'] = 'vencido'
                elif epi['epi_data_validade'] <= alert_date:
                    epi['status_validade'] = 'alerta'
                else:
                    epi['status_validade'] = 'ok'
            else:
                epi['status_validade'] = 'indefinido'
        
        context = get_template_context()
        context.update({
            'funcionario': funcionario,
            'funcionario_id': funcionario_id,
            'epis': epis,
            'current_date': current_date,
            'breadcrumbs': generate_breadcrumbs('epis', funcionario_id=funcionario_id),
            'format_date': format_date
        })
        
        return render_template('epis/listar.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao listar EPIs do funcionário {funcionario_id}: {e}")
        flash("Erro ao carregar EPIs", "error")
        return redirect(url_for('funcionarios.detalhes', funcionario_id=funcionario_id))

@epis_bp.route('/funcionario/<int:funcionario_id>/adicionar', methods=['GET', 'POST'])
@require_login
def adicionar_epi(funcionario_id):
    """
    Adiciona novo EPI para um funcionário.
    """
    if request.method == 'POST':
        try:
            # Extrair dados do formulário
            epi_nome = request.form.get('epi_nome', '').strip()
            epi_ca = request.form.get('epi_ca', '').strip()
            epi_data_entrega = request.form.get('epi_data_entrega', '').strip()
            epi_data_validade = request.form.get('epi_data_validade', '').strip()
            epi_observacoes = request.form.get('epi_observacoes', '').strip()
            
            # Validação básica
            if not epi_nome:
                flash("Nome do EPI é obrigatório", "error")
                return redirect(request.url)
            
            # Inserir no banco
            sql = """
            INSERT INTO epis (funcionario_id, epi_nome, epi_ca, epi_data_entrega, epi_data_validade, epi_observacoes)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            params = (
                funcionario_id,
                epi_nome,
                epi_ca or None,
                epi_data_entrega or None,
                epi_data_validade or None,
                epi_observacoes or None
            )
            
            DatabaseManager.execute_query(sql, params, fetch_all=False)
            
            flash("EPI adicionado com sucesso", "success")
            logger.info(f"EPI {epi_nome} adicionado para funcionário {funcionario_id} por {get_current_user()['usuario']}")
            
            return redirect(url_for('epis.listar_epis', funcionario_id=funcionario_id))
            
        except Exception as e:
            logger.error(f"Erro ao adicionar EPI: {e}")
            flash("Erro ao adicionar EPI", "error")
            return redirect(request.url)
    
    # GET: Exibir formulário
    try:
        funcionario = DatabaseManager.execute_query(
            "SELECT nome_completo FROM funcionarios WHERE id = %s",
            (funcionario_id,),
            fetch_one=True
        )
        
        if not funcionario:
            flash("Funcionário não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        context = get_template_context()
        context.update({
            'funcionario': funcionario,
            'funcionario_id': funcionario_id,
            'breadcrumbs': generate_breadcrumbs('epi_adicionar', funcionario_id=funcionario_id)
        })
        
        return render_template('epis/adicionar.html', **context)
        
    except Exception as e:
        logger.error(f"Erro ao exibir formulário de EPI: {e}")
        flash("Erro ao carregar formulário", "error")
        return redirect(url_for('funcionarios.detalhes', funcionario_id=funcionario_id))

@epis_bp.route('/<int:epi_id>/excluir', methods=['POST'])
@require_admin
def excluir_epi(epi_id):
    """
    Exclui um EPI.
    """
    try:
        # Buscar EPI para obter funcionario_id
        epi = DatabaseManager.execute_query(
            "SELECT funcionario_id, epi_nome FROM epis WHERE id = %s",
            (epi_id,),
            fetch_one=True
        )
        
        if not epi:
            flash("EPI não encontrado", "error")
            return redirect(url_for('funcionarios.index'))
        
        # Excluir
        DatabaseManager.execute_query(
            "DELETE FROM epis WHERE id = %s",
            (epi_id,),
            fetch_all=False
        )
        
        flash(f"EPI {epi['epi_nome']} excluído com sucesso", "success")
        logger.info(f"EPI {epi_id} excluído por {get_current_user()['usuario']}")
        
        return redirect(url_for('epis.listar_epis', funcionario_id=epi['funcionario_id']))
        
    except Exception as e:
        logger.error(f"Erro ao excluir EPI {epi_id}: {e}")
        flash("Erro ao excluir EPI", "error")
        return redirect(url_for('funcionarios.index')) 