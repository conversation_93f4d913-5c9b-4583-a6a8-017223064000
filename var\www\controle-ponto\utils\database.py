#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Utilitários de Banco de Dados - Controle de Ponto
--------------------------------------------------

Este módulo centraliza todas as operações de banco de dados,
proporcionando uma interface consistente e reutilizável.
"""

import logging
import pymysql
from pymysql.cursors import DictCursor
from contextlib import contextmanager

# Importa configurações seguras
try:
    from .config import Config
    logger = logging.getLogger('controle-ponto.database')
    logger.info("✅ Usando configurações seguras do módulo config")
except ImportError:
    logger = logging.getLogger('controle-ponto.database')
    logger.warning("⚠️ Módulo config não encontrado - usando configurações de fallback")
    Config = None

# ✅ CONFIGURAÇÕES ATUALIZADAS - CONFORME GUIA.MARKDOWN
# Configurações corrigidas com credenciais do servidor remoto
LEGACY_DB_CONFIGS = [
    {
        'host': '************',
        'user': 'cavalcrod',
        'password': '200381',
        'database': 'controle_ponto',
        'charset': 'utf8mb4',
        'cursorclass': DictCursor,
        'name': 'config_principal_corrigida'
    },
    {
        'host': '************', 
        'user': 'root',
        'password': '200381',
        'database': 'controle_ponto',
        'charset': 'utf8mb4',
        'cursorclass': DictCursor,
        'name': 'config_root_servidor'
    },
    {
        'host': '************',
        'user': 'controle_user',
        'password': 'controle123',
        'database': 'controle_ponto',
        'charset': 'utf8mb4',
        'cursorclass': DictCursor,
        'name': 'config_fallback_legacy'
    }
]

def get_db_configs():
    """
    Retorna lista de configurações de banco de dados.
    Prioriza configurações seguras do config.py, depois usa fallbacks legacy.
    
    Returns:
        list: Lista de configurações de banco de dados
    """
    configs = []
    
    if Config:
        # ✅ CONFIGURAÇÃO SEGURA - Prioridade máxima
        safe_config = Config.get_db_config()
        safe_config.update({
            'cursorclass': DictCursor,
            'name': 'config_segura_env'
        })
        configs.append(safe_config)
        logger.info("✅ Adicionada configuração segura baseada em variáveis de ambiente")
    
    # ⚠️ FALLBACKS LEGACY - Apenas para compatibilidade
    configs.extend(LEGACY_DB_CONFIGS)
    logger.warning("⚠️ Adicionadas configurações legacy - migre para .env o mais rápido possível!")
    
    return configs

def get_db_connection():
    """
    Cria uma conexão com o banco de dados.
    Tenta múltiplas configurações até encontrar uma que funcione.
    
    Returns:
        pymysql.Connection: Conexão ativa com o banco
    
    Raises:
        Exception: Erro ao conectar ao banco de dados
    """
    last_error = None
    configs = get_db_configs()
    
    for config in configs:
        try:
            config_copy = config.copy()
            name = config_copy.pop('name')
            connection = pymysql.connect(**config_copy)
            
            # Log diferenciado para configurações seguras vs legacy
            if 'segura' in name:
                logger.info(f"✅ Conectado ao banco usando {name}")
            else:
                logger.warning(f"⚠️ Conectado usando config legacy: {name}")
                
            return connection
        except Exception as e:
            last_error = e
            logger.warning(f"Falha na conexão com {config['name']}: {e}")
            continue
    
    # Se chegou aqui, nenhuma configuração funcionou
    logger.error(f"Erro ao conectar ao banco de dados com todas as configurações: {last_error}")
    raise Exception(f"Não foi possível conectar ao banco: {last_error}")

@contextmanager
def get_db_cursor():
    """
    Context manager para obter cursor do banco de dados.
    Gerencia automaticamente conexão e transações.
    
    Yields:
        tuple: (connection, cursor)
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        yield conn, cursor
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Erro na operação de banco: {e}")
        raise
    finally:
        if conn:
            conn.close()

class DatabaseManager:
    """
    Classe para gerenciar operações avançadas do banco de dados.
    """
    
    @staticmethod
    def execute_query(query, params=None, fetch_one=False, fetch_all=True):
        """
        Executa uma query no banco de dados.
        
        Args:
            query (str): Query SQL a ser executada
            params (tuple): Parâmetros da query
            fetch_one (bool): Se deve retornar apenas um resultado
            fetch_all (bool): Se deve retornar todos os resultados
            
        Returns:
            dict/list/None: Resultado da query
            
        Raises:
            Exception: Erro de conexão ou execução da query
        """
        try:
            with get_db_cursor() as (conn, cursor):
                cursor.execute(query, params or ())
                
                if fetch_one:
                    return cursor.fetchone()
                elif fetch_all:
                    return cursor.fetchall()
                else:
                    conn.commit()
                    return cursor.lastrowid
        except Exception as e:
            # Melhor tratamento de erros específicos
            error_msg = str(e)
            if "Can't connect" in error_msg or "Connection refused" in error_msg:
                logger.error(f"Erro de conexão com banco de dados: {e}")
                raise Exception("Banco de dados indisponível. Verifique se o MySQL está rodando.")
            elif "Access denied" in error_msg:
                logger.error(f"Erro de autenticação no banco: {e}")
                if "***********" in error_msg:
                    raise Exception("Acesso negado: IP *********** não tem permissão para conectar ao MySQL. Configure as permissões do usuário no servidor.")
                else:
                    raise Exception("Erro de autenticação no banco de dados.")
            elif "Unknown database" in error_msg:
                logger.error(f"Database não encontrada: {e}")
                raise Exception("Base de dados 'controle_ponto' não encontrada.")
            else:
                logger.error(f"Erro na query SQL: {e}")
                raise Exception(f"Erro na operação do banco: {e}")
    
    @staticmethod
    def execute_transaction(queries_and_params):
        """
        Executa múltiplas queries em uma transação.
        
        Args:
            queries_and_params (list): Lista de tuplas (query, params)
            
        Returns:
            bool: True se a transação foi bem-sucedida
        """
        with get_db_cursor() as (conn, cursor):
            try:
                for query, params in queries_and_params:
                    cursor.execute(query, params or ())
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                logger.error(f"Erro na transação: {e}")
                raise
    
    @staticmethod
    def get_paginated_results(query, params=None, page=1, per_page=10):
        """
        Executa uma query com paginação.
        
        Args:
            query (str): Query SQL base
            params (tuple): Parâmetros da query
            page (int): Número da página (1-indexed)
            per_page (int): Registros por página
            
        Returns:
            dict: Dicionário com dados paginados
        """
        # Calcula offset
        offset = (page - 1) * per_page
        
        # Query para contar total de registros
        count_query = f"SELECT COUNT(*) as total FROM ({query}) as count_table"
        total_count = DatabaseManager.execute_query(count_query, params, fetch_one=True)['total']
        
        # Query paginada
        paginated_query = f"{query} LIMIT %s OFFSET %s"
        paginated_params = list(params or ()) + [per_page, offset]
        
        results = DatabaseManager.execute_query(paginated_query, tuple(paginated_params))
        
        # Calcula informações de paginação
        total_pages = (total_count + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages
        
        return {
            'data': results,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': total_pages,
                'has_prev': has_prev,
                'has_next': has_next,
                'prev_num': page - 1 if has_prev else None,
                'next_num': page + 1 if has_next else None
            }
        }

# Funções específicas para funcionários
class FuncionarioQueries:
    """Queries específicas para a tabela de funcionários."""
    
    @staticmethod
    def get_all(page=1, per_page=10, search=None, status=None):
        """
        Obtém todos os funcionários com paginação e filtros.
        
        Args:
            page (int): Página atual
            per_page (int): Registros por página
            search (str): Termo de busca
            status (str): Filtro por status
            
        Returns:
            dict: Dados paginados dos funcionários
        """
        base_query = "SELECT * FROM funcionarios"
        where_conditions = []
        params = []
        
        if search:
            where_conditions.append("(nome_completo LIKE %s OR cpf LIKE %s OR matricula_empresa LIKE %s)")
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])
        
        if status:
            where_conditions.append("status_cadastro = %s")
            params.append(status)
        
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        
        base_query += " ORDER BY nome_completo"
        
        return DatabaseManager.get_paginated_results(base_query, tuple(params), page, per_page)
    
    @staticmethod
    def get_by_id(funcionario_id):
        """
        Obtém um funcionário por ID.
        
        Args:
            funcionario_id (int): ID do funcionário
            
        Returns:
            dict: Dados do funcionário
        """
        query = "SELECT * FROM funcionarios WHERE id = %s"
        return DatabaseManager.execute_query(query, (funcionario_id,), fetch_one=True)
    
    @staticmethod
    def get_with_epis(funcionario_id):
        """
        Obtém funcionário com seus EPIs e dados de jornada da empresa.

        ✅ CORREÇÃO CRÍTICA: Usa apenas dados da jornada da empresa como fonte única.
        Remove conflito entre campos individuais e jornada da empresa.

        Args:
            funcionario_id (int): ID do funcionário

        Returns:
            dict: Funcionário com EPIs e jornada da empresa
        """
        # ✅ CORREÇÃO CRÍTICA: Query considerando alocações ativas
        funcionario_query = """
        SELECT
            -- ✅ APENAS dados básicos do funcionário (SEM campos de jornada individual)
            f.id, f.empresa_id, f.cliente_atual_id, f.jornada_trabalho_id,
            f.nome_completo, f.cpf, f.rg, f.data_nascimento, f.sexo, f.estado_civil, f.nacionalidade,
            f.ctps_numero, f.ctps_serie_uf, f.pis_pasep,
            f.endereco_rua, f.endereco_bairro, f.endereco_cidade, f.endereco_cep, f.endereco_estado,
            f.telefone1, f.telefone2, f.email,
            f.cargo, f.setor, f.setor_obra, f.empresa, f.matricula_empresa, f.data_admissao, f.tipo_contrato,
            f.digital_dedo1, f.digital_dedo2, f.foto_3x4, f.foto_url,
            f.nivel_acesso, f.turno, f.tolerancia_ponto, f.banco_horas, f.hora_extra,
            f.ativo, f.status_cadastro, f.data_cadastro, f.data_atualizacao,
            f.biometria_qualidade_1, f.biometria_qualidade_2, f.biometria_data_cadastro,
            f.salario_base, f.tipo_pagamento, f.valor_hora, f.valor_hora_extra, f.percentual_hora_extra,
            f.vale_transporte, f.vale_alimentacao, f.outros_beneficios,
            f.desconto_inss, f.desconto_irrf, f.observacoes_pagamento, f.data_ultima_alteracao_salario,
            f.biometria_qualidade, f.ultimo_login, f.tentativas_biometria, f.status_biometria, f.status,
            f.epi_obrigatorio_json, f.epi_treinamento_data, f.epi_responsavel, f.epi_observacoes, f.epi_termo_assinado,
            f.horas_trabalho_obrigatorias,

            -- Dados da empresa
            e.nome_fantasia as empresa_nome,
            e.razao_social as empresa_razao_social,

            -- ✅ PRIORIDADE 1: Jornada da alocação ativa (se existir)
            COALESCE(jt_alocacao.nome_jornada, jt.nome_jornada) as nome_jornada,
            COALESCE(jt_alocacao.tipo_jornada, jt.tipo_jornada) as tipo_jornada,
            COALESCE(jt_alocacao.seg_qui_entrada, jt.seg_qui_entrada) as jornada_seg_qui_entrada,
            COALESCE(jt_alocacao.seg_qui_saida, jt.seg_qui_saida) as jornada_seg_qui_saida,
            COALESCE(jt_alocacao.sexta_entrada, jt.sexta_entrada) as jornada_sex_entrada,
            COALESCE(jt_alocacao.sexta_saida, jt.sexta_saida) as jornada_sex_saida,
            COALESCE(jt_alocacao.intervalo_inicio, jt.intervalo_inicio) as jornada_intervalo_entrada,
            COALESCE(jt_alocacao.intervalo_fim, jt.intervalo_fim) as jornada_intervalo_saida,
            COALESCE(jt_alocacao.tolerancia_entrada_minutos, jt.tolerancia_entrada_minutos) as tolerancia_entrada_minutos,

            -- Dados do horário (tabela horarios_trabalho) - fallback apenas se necessário
            ht.nome_horario,
            ht.entrada_manha,
            ht.saida_almoco,
            ht.entrada_tarde,
            ht.saida,
            ht.tolerancia_minutos,

            -- ✅ Informações de alocação
            fa.id as alocacao_id,
            fa.empresa_cliente_id,
            ec.razao_social as cliente_nome,
            fa.cargo_no_cliente

        FROM funcionarios f
        LEFT JOIN empresas e ON f.empresa_id = e.id

        -- ✅ PRIORIDADE: Buscar alocação ativa primeiro
        LEFT JOIN funcionario_alocacoes fa ON f.id = fa.funcionario_id AND fa.ativo = TRUE
        LEFT JOIN empresas ec ON fa.empresa_cliente_id = ec.id
        LEFT JOIN jornadas_trabalho jt_alocacao ON fa.jornada_trabalho_id = jt_alocacao.id

        -- ✅ FALLBACK: Jornada da empresa do funcionário
        LEFT JOIN jornadas_trabalho jt ON f.jornada_trabalho_id = jt.id
        LEFT JOIN horarios_trabalho ht ON f.horario_trabalho_id = ht.id

        WHERE f.id = %s
        """

        funcionario = DatabaseManager.execute_query(funcionario_query, (funcionario_id,), fetch_one=True)

        if funcionario:
            # ✅ LÓGICA DE PRIORIDADE PARA JORNADAS
            if funcionario.get('alocacao_id'):
                # Funcionário ALOCADO - jornada vem da alocação
                logger.info(f"✅ Funcionário {funcionario_id}: ALOCADO para '{funcionario.get('cliente_nome', 'N/A')}' - usando jornada da alocação")
                logger.info(f"   📋 Jornada: '{funcionario.get('nome_jornada', 'N/A')}'")
            elif funcionario.get('jornada_seg_qui_entrada'):
                # Funcionário NÃO alocado - jornada vem da empresa
                logger.info(f"✅ Funcionário {funcionario_id}: NÃO alocado - usando jornada da empresa '{funcionario.get('nome_jornada', 'N/A')}'")
            elif funcionario.get('entrada_manha'):
                # Fallback para horários antigos
                logger.info(f"🔄 Funcionário {funcionario_id}: Usando fallback para horários (sem jornada)")
                funcionario['jornada_seg_qui_entrada'] = funcionario['entrada_manha']
                funcionario['jornada_seg_qui_saida'] = funcionario['saida']
                funcionario['jornada_sex_entrada'] = funcionario['entrada_manha']
                funcionario['jornada_sex_saida'] = funcionario['saida']
                funcionario['jornada_intervalo_entrada'] = funcionario['saida_almoco']
                funcionario['jornada_intervalo_saida'] = funcionario['entrada_tarde']
            else:
                logger.warning(f"⚠️ Funcionário {funcionario_id}: Nenhuma jornada encontrada")

            # Buscar EPIs do funcionário
            try:
                epis_query = "SELECT * FROM epis WHERE funcionario_id = %s ORDER BY epi_data_entrega DESC"
                funcionario['epis'] = DatabaseManager.execute_query(epis_query, (funcionario_id,))
            except Exception as e:
                # Se tabela EPIs não existir ou houver erro, inicializa lista vazia
                logger.warning(f"Erro ao buscar EPIs para funcionário {funcionario_id}: {e}")
                funcionario['epis'] = []
        return funcionario
    
    @staticmethod
    def delete_funcionario(funcionario_id):
        """
        Exclui um funcionário e seus dados relacionados.
        
        Args:
            funcionario_id (int): ID do funcionário
            
        Returns:
            bool: True se excluído com sucesso
        """
        try:
            with get_db_cursor() as (conn, cursor):
                # Exclui EPIs se a tabela existir
                try:
                    cursor.execute("DELETE FROM epis WHERE funcionario_id = %s", (funcionario_id,))
                except Exception as e:
                    logger.warning(f"Erro ao excluir EPIs (tabela pode não existir): {e}")
                
                # Exclui registros de ponto se a tabela existir
                try:
                    cursor.execute("DELETE FROM registros_ponto WHERE funcionario_id = %s", (funcionario_id,))
                except Exception as e:
                    logger.warning(f"Erro ao excluir registros de ponto (tabela pode não existir): {e}")
                
                # Exclui o funcionário (obrigatório)
                cursor.execute("DELETE FROM funcionarios WHERE id = %s", (funcionario_id,))
                
                if cursor.rowcount == 0:
                    logger.warning(f"Nenhum funcionário encontrado com ID {funcionario_id}")
                    return False
                
                conn.commit()
                logger.info(f"Funcionário {funcionario_id} e dados relacionados excluídos com sucesso")
                return True
                
        except Exception as e:
            logger.error(f"Erro ao excluir funcionário {funcionario_id}: {e}")
            return False
    
    @staticmethod
    def get_next_matricula():
        """
        Obtém a próxima matrícula disponível.
        
        Returns:
            str: Próxima matrícula formatada
        """
        try:
            query = "SELECT MAX(CAST(matricula_empresa AS UNSIGNED)) as max_matricula FROM funcionarios"
            result = DatabaseManager.execute_query(query, fetch_one=True)
            
            if result['max_matricula'] is None:
                next_num = 1
            else:
                next_num = result['max_matricula'] + 1
                
            return f"{next_num:04d}"
        except Exception as e:
            logger.error(f"Erro ao obter próxima matrícula: {e}")
            return "0001" 