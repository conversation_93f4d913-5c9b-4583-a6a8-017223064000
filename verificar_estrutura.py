#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para verificar a estrutura da tabela empresas
Data: 03/07/2025
"""

from utils.database import get_db_connection

def main():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Verificar a estrutura da tabela empresas
        cursor.execute("DESCRIBE empresas")
        colunas = cursor.fetchall()
        
        print("Estrutura da tabela empresas:")
        for coluna in colunas:
            print(f"- {coluna['Field']}: {coluna['Type']} (Null: {coluna['Null']}, Default: {coluna['Default']})")
        
        # Verificar se a coluna 'ativa' é atualizável
        empresa_id = 7  # ID da empresa de teste
        
        # Tentar atualizar diretamente
        print(f"\nTentando atualizar diretamente a coluna 'ativa' da empresa ID {empresa_id}...")
        cursor.execute("UPDATE empresas SET ativa = FALSE WHERE id = %s", (empresa_id,))
        conn.commit()
        
        # Verificar se a atualização funcionou
        cursor.execute("SELECT id, razao_social, ativa FROM empresas WHERE id = %s", (empresa_id,))
        empresa = cursor.fetchone()
        
        if empresa:
            ativa = empresa['ativa'] if isinstance(empresa, dict) else empresa[2]
            print(f"Empresa após atualização direta - ID: {empresa_id}, Ativa: {ativa}")
            
            if not ativa:
                print(f"✅ Atualização direta funcionou! A empresa ID {empresa_id} foi marcada como inativa.")
            else:
                print(f"❌ Atualização direta falhou! A empresa ID {empresa_id} ainda está ativa.")
        else:
            print(f"❌ Empresa ID {empresa_id} não encontrada!")
        
        conn.close()
        
    except Exception as e:
        print(f"Erro: {str(e)}")

if __name__ == "__main__":
    main()