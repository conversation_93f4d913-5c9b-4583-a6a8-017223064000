{% extends "base.html" %}

{% block content %}
<style>
/* Estilos customizados para dashboard profissional */
.dashboard-container {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding: 0;
}

.dashboard-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
}

.dashboard-title {
    color: #343a40;
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0;
}

.dashboard-subtitle {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.status-badge {
    background: #e8f5e8;
    color: #198754;
    border: 1px solid #b8e5b8;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-indicator {
    width: 6px;
    height: 6px;
    background: #198754;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.kpi-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1.5rem;
    height: 100%;
    transition: box-shadow 0.2s ease;
}

.kpi-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.kpi-icon {
    width: 40px !important;
    height: 40px !important;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.kpi-icon svg {
    width: 20px !important;
    height: 20px !important;
    max-width: 20px !important;
    max-height: 20px !important;
}

.kpi-icon.blue { background: #e7f3ff; }
.kpi-icon.green { background: #e8f5e8; }
.kpi-icon.purple { background: #f3e8ff; }
.kpi-icon.orange { background: #fff3e0; }

.kpi-icon.blue svg { color: #0d6efd; }
.kpi-icon.green svg { color: #198754; }
.kpi-icon.purple svg { color: #6f42c1; }
.kpi-icon.orange svg { color: #fd7e14; }

.kpi-label {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.kpi-value {
    color: #343a40;
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.kpi-trend {
    font-size: 0.875rem;
    font-weight: 500;
}

.kpi-trend.positive { color: #198754; }
.kpi-trend.negative { color: #dc3545; }

.chart-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
}

.chart-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.chart-title {
    color: #343a40;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.chart-subtitle {
    color: #6c757d;
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.chart-body {
    padding: 1.5rem;
}

.legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.legend-label {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 0.875rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.legend-color.purple { background: #8b5cf6; }
.legend-color.orange { background: #f59e0b; }

.legend-value {
    color: #343a40;
    font-weight: 500;
    font-size: 0.875rem;
}

.action-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.action-card:hover {
    border-color: #0d6efd;
    background: #f8f9ff;
    text-decoration: none;
    color: inherit;
}

.action-card:hover .action-icon.blue {
    background: #cce7ff;
}

.action-card:hover.green-hover {
    border-color: #198754;
    background: #f8fff8;
}

.action-card:hover.green-hover .action-icon.green {
    background: #d4f5d4;
}

.action-card:hover.purple-hover {
    border-color: #6f42c1;
    background: #faf8ff;
}

.action-card:hover.purple-hover .action-icon.purple {
    background: #e8d8ff;
}

.action-icon {
    width: 40px !important;
    height: 40px !important;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    transition: background 0.2s ease;
}

.action-icon svg {
    width: 20px !important;
    height: 20px !important;
    max-width: 20px !important;
    max-height: 20px !important;
}

.action-title {
    color: #343a40;
    font-weight: 500;
    margin: 0;
    font-size: 0.95rem;
}

.action-subtitle {
    color: #6c757d;
    font-size: 0.75rem;
    margin: 0;
}

.chart-container {
    position: relative;
    height: 300px;
}

.chart-container canvas {
    max-width: 100%;
    height: auto;
}

/* FORÇA DIMENSÕES DOS SVGS PARA EVITAR ÍCONES GIGANTES */
svg {
    max-width: 24px !important;
    max-height: 24px !important;
}

.kpi-icon svg,
.action-icon svg {
    width: 20px !important;
    height: 20px !important;
    max-width: 20px !important;
    max-height: 20px !important;
    min-width: 20px !important;
    min-height: 20px !important;
}

.dashboard-header .kpi-icon svg {
    width: 24px !important;
    height: 24px !important;
    max-width: 24px !important;
    max-height: 24px !important;
}

/* Ensure proper sizing for all SVG elements */
.dashboard-container svg {
    flex-shrink: 0;
    display: block;
}
</style>

<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="kpi-icon blue me-3">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <div>
                            <h1 class="dashboard-title">{{ titulo }}</h1>
                            <p class="dashboard-subtitle">Dashboard • {{ periodo }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <span class="status-badge">
                        <div class="status-indicator d-inline-block"></div>
                        Sistema Ativo
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        
        <!-- KPI Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="kpi-card">
                    <div class="kpi-icon blue">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <div class="kpi-label">Total de Registros</div>
                    <div class="d-flex align-items-end">
                        <div class="kpi-value me-2">{{ "{:,}".format(stats.total_registros) }}</div>
                        <div class="kpi-trend positive">+12.3%</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="kpi-card">
                    <div class="kpi-icon green">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                    </div>
                    <div class="kpi-label">Funcionários Ativos</div>
                    <div class="d-flex align-items-end">
                        <div class="kpi-value me-2">{{ "{:,}".format(stats.funcionarios_ativos) }}</div>
                        <div class="kpi-trend positive">+5.2%</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="kpi-card">
                    <div class="kpi-icon purple">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0h-2m8-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <div class="kpi-label">Registros Biométricos</div>
                    <div class="d-flex align-items-end">
                        <div class="kpi-value me-2">{{ "{:,}".format(stats.registros_biometricos) }}</div>
                        <div class="badge badge-secondary">{{ stats.percentual_biometrico }}%</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="kpi-card">
                    <div class="kpi-icon orange">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                    </div>
                    <div class="kpi-label">Registros Manuais</div>
                    <div class="d-flex align-items-end">
                        <div class="kpi-value me-2">{{ "{:,}".format(stats.registros_manuais) }}</div>
                        <div class="kpi-trend negative">-2.1%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-lg-8 mb-3">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Registros por Período</h3>
                        <p class="chart-subtitle">Últimos 7 dias de atividade</p>
                    </div>
                    <div class="chart-body">
                        <div class="chart-container">
                            <canvas id="chartRegistrosDiarios"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 mb-3">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Distribuição de Métodos</h3>
                        <p class="chart-subtitle">Biométrico vs Manual</p>
                    </div>
                    <div class="chart-body">
                        <div class="chart-container" style="height: 200px;">
                            <canvas id="chartMetodos"></canvas>
                        </div>
                        <div class="mt-3">
                            <div class="legend-item">
                                <div class="legend-label">
                                    <div class="legend-color purple"></div>
                                    Biométrico
                                </div>
                                <div class="legend-value">{{ stats.percentual_biometrico }}%</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-label">
                                    <div class="legend-color orange"></div>
                                    Manual
                                </div>
                                <div class="legend-value">{{ 100 - stats.percentual_biometrico }}%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pontualidade Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Análise de Pontualidade</h3>
                        <p class="chart-subtitle">Controle de atrasos por período</p>
                    </div>
                    <div class="chart-body">
                        <div class="chart-container">
                            <canvas id="chartPontualidade"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Ações Rápidas</h3>
                        <p class="chart-subtitle">Acesso direto às principais funcionalidades</p>
                    </div>
                    <div class="chart-body">
                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <a href="/relatorios/pontos" class="action-card d-flex align-items-center">
                                    <div class="action-icon blue">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="action-title">Relatórios</div>
                                        <div class="action-subtitle">Ver detalhados</div>
                                    </div>
                                </a>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <a href="/registro-ponto/manual" class="action-card green-hover d-flex align-items-center">
                                    <div class="action-icon green">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="action-title">Ponto Manual</div>
                                        <div class="action-subtitle">Registrar agora</div>
                                    </div>
                                </a>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <a href="/registro-ponto/biometrico" class="action-card purple-hover d-flex align-items-center">
                                    <div class="action-icon purple">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0h-2m8-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="action-title">Ponto Biométrico</div>
                                        <div class="action-subtitle">Capturar digital</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart data preparation
    var chartsData = {
        labels: {{ graficos.labels_dias | tojson }},
        registrosDiarios: {{ graficos.dados_registros_diarios | tojson }},
        pontualidade: {{ graficos.dados_pontualidade | tojson }},
        biometricos: {{ stats.registros_biometricos }},
        manuais: {{ stats.registros_manuais }}
    };

    // Chart.js configuration
    Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#6c757d';

    // Base chart options
    var baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                border: {
                    display: false
                },
                grid: {
                    color: '#f3f4f6'
                },
                ticks: {
                    color: '#6c757d'
                }
            },
            x: {
                border: {
                    display: false
                },
                grid: {
                    display: false
                },
                ticks: {
                    color: '#6c757d'
                }
            }
        }
    };

    // Bar Chart - Daily Records
    var ctxBar = document.getElementById('chartRegistrosDiarios');
    if (ctxBar) {
        new Chart(ctxBar, {
            type: 'bar',
            data: {
                labels: chartsData.labels,
                datasets: [{
                    label: 'Registros',
                    data: chartsData.registrosDiarios,
                    backgroundColor: '#0d6efd',
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: baseOptions
        });
    }

    // Doughnut Chart - Methods
    var ctxDoughnut = document.getElementById('chartMetodos');
    if (ctxDoughnut) {
        new Chart(ctxDoughnut, {
            type: 'doughnut',
            data: {
                labels: ['Biométrico', 'Manual'],
                datasets: [{
                    data: [chartsData.biometricos, chartsData.manuais],
                    backgroundColor: ['#8b5cf6', '#f59e0b'],
                    borderColor: '#ffffff',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // Line Chart - Punctuality
    var ctxLine = document.getElementById('chartPontualidade');
    if (ctxLine) {
        new Chart(ctxLine, {
            type: 'line',
            data: {
                labels: chartsData.labels,
                datasets: [{
                    label: 'Atrasos',
                    data: chartsData.pontualidade,
                    fill: true,
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderColor: '#dc3545',
                    borderWidth: 2,
                    tension: 0.3,
                    pointBackgroundColor: '#dc3545',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 3
                }]
            },
            options: baseOptions
        });
    }
});
</script>
{% endblock %} 