#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Sistema de Relatórios de Banco de Horas
Sistema: RLPONTO-WEB v1.0
Data: 25/06/2025
Desenvolvido por: AiNexus Tecnologia - Richardson Rodrigues

Relatórios e dashboards para o sistema de banco de horas personalizado
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, timedelta
from utils.auth import require_login, require_admin
from utils.database import get_db_connection
from pymysql.cursors import DictCursor
import logging
import calendar

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criar blueprint
banco_horas_bp = Blueprint('banco_horas', __name__, url_prefix='/banco-horas')

@banco_horas_bp.route('/')
@require_login
def index():
    """Dashboard principal do banco de horas"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter dados do usuário logado
        usuario_id = session.get('user_id')
        nivel_acesso = session.get('nivel_acesso', 'usuario')
        
        # Se for usuário comum, mostrar apenas seus dados
        if nivel_acesso == 'usuario':
            # Buscar funcionário associado ao usuário
            cursor.execute("""
                SELECT id, nome_completo 
                FROM funcionarios 
                WHERE usuario_id = %s AND status_cadastro = 'Ativo'
            """, (usuario_id,))
            funcionario = cursor.fetchone()
            
            if not funcionario:
                return render_template('banco_horas/sem_acesso.html')
            
            funcionario_id = funcionario['id']
            dados = obter_resumo_funcionario(cursor, funcionario_id)
            
            return render_template('banco_horas/funcionario.html', 
                                 funcionario=funcionario, 
                                 dados=dados)
        
        # Para administradores, mostrar dashboard geral
        else:
            dados_gerais = obter_dashboard_geral(cursor)
            return render_template('banco_horas/dashboard.html', dados=dados_gerais)
            
    except Exception as e:
        logger.error(f"Erro no dashboard de banco de horas: {e}")
        return render_template('erro.html', 
                             mensagem="Erro ao carregar dashboard de banco de horas")
    finally:
        if 'conn' in locals():
            conn.close()

@banco_horas_bp.route('/relatorio-mensal')
@require_admin
def relatorio_mensal():
    """Relatório mensal de banco de horas"""
    try:
        # Obter parâmetros
        ano = request.args.get('ano', datetime.now().year, type=int)
        mes = request.args.get('mes', datetime.now().month, type=int)
        funcionario_id = request.args.get('funcionario_id', type=int)
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter dados do relatório
        dados = obter_relatorio_mensal(cursor, ano, mes, funcionario_id)
        
        # Obter lista de funcionários para filtro
        cursor.execute("""
            SELECT id, nome_completo 
            FROM funcionarios 
            WHERE status_cadastro = 'Ativo'
            ORDER BY nome_completo
        """)
        funcionarios = list(cursor.fetchall())
        
        return render_template('banco_horas/relatorio_mensal.html',
                             dados=dados,
                             funcionarios=funcionarios,
                             ano=ano,
                             mes=mes,
                             funcionario_id=funcionario_id,
                             nome_mes=calendar.month_name[mes])
        
    except Exception as e:
        logger.error(f"Erro no relatório mensal: {e}")
        return render_template('erro.html', 
                             mensagem="Erro ao gerar relatório mensal")
    finally:
        if 'conn' in locals():
            conn.close()

@banco_horas_bp.route('/justificativas')
@require_admin
def justificativas():
    """Gerenciar justificativas pendentes"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Obter justificativas pendentes
        cursor.execute("""
            SELECT 
                j.id,
                j.funcionario_id,
                f.nome_completo,
                j.data_referencia,
                j.tipo_justificativa,
                j.motivo,
                j.minutos_justificados,
                j.status_aprovacao,
                j.criado_em,
                u.nome as solicitado_por_nome
            FROM justificativas_ponto j
            INNER JOIN funcionarios f ON j.funcionario_id = f.id
            LEFT JOIN usuarios u ON j.solicitado_por = u.id
            WHERE j.status_aprovacao = 'pendente'
            ORDER BY j.criado_em DESC
        """)
        
        justificativas_pendentes = list(cursor.fetchall())
        
        return render_template('banco_horas/justificativas.html',
                             justificativas=justificativas_pendentes)
        
    except Exception as e:
        logger.error(f"Erro ao carregar justificativas: {e}")
        return render_template('erro.html', 
                             mensagem="Erro ao carregar justificativas")
    finally:
        if 'conn' in locals():
            conn.close()

@banco_horas_bp.route('/api/aprovar-justificativa', methods=['POST'])
@require_admin
def aprovar_justificativa():
    """API para aprovar/rejeitar justificativas"""
    try:
        data = request.get_json()
        justificativa_id = data.get('justificativa_id')
        acao = data.get('acao')  # 'aprovar' ou 'rejeitar'
        observacoes = data.get('observacoes', '')
        
        if not justificativa_id or acao not in ['aprovar', 'rejeitar']:
            return jsonify({'success': False, 'message': 'Dados inválidos'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor(DictCursor)
        
        # Atualizar justificativa
        status = 'aprovado' if acao == 'aprovar' else 'rejeitado'
        cursor.execute("""
            UPDATE justificativas_ponto 
            SET status_aprovacao = %s,
                aprovado_por = %s,
                data_aprovacao = NOW(),
                observacoes_aprovacao = %s
            WHERE id = %s
        """, (status, session.get('user_id'), observacoes, justificativa_id))
        
        conn.commit()
        
        return jsonify({
            'success': True, 
            'message': f'Justificativa {status} com sucesso'
        })
        
    except Exception as e:
        logger.error(f"Erro ao processar justificativa: {e}")
        return jsonify({'success': False, 'message': 'Erro interno'}), 500
    finally:
        if 'conn' in locals():
            conn.close()

def obter_resumo_funcionario(cursor, funcionario_id):
    """Obtém resumo do banco de horas de um funcionário"""
    try:
        # Saldo atual do mês
        hoje = date.today()
        primeiro_dia = date(hoje.year, hoje.month, 1)
        
        cursor.execute("""
            SELECT 
                SUM(saldo_liquido_minutos) as saldo_mensal,
                SUM(atraso_entrada_minutos) as total_atrasos,
                SUM(excesso_almoco_minutos) as total_excesso_almoco,
                SUM(saida_antecipada_minutos) as total_saidas_antecipadas,
                SUM(horas_extras_minutos) as total_horas_extras,
                COUNT(*) as dias_trabalhados,
                SUM(CASE WHEN status_dia = 'completo' THEN 1 ELSE 0 END) as dias_completos
            FROM banco_horas
            WHERE funcionario_id = %s 
            AND data_referencia >= %s 
            AND data_referencia <= %s
        """, (funcionario_id, primeiro_dia, hoje))
        
        resumo_mensal = cursor.fetchone()
        
        # Últimos registros
        cursor.execute("""
            SELECT 
                data_referencia,
                saldo_liquido_minutos,
                status_dia,
                observacoes
            FROM banco_horas
            WHERE funcionario_id = %s
            ORDER BY data_referencia DESC
            LIMIT 10
        """, (funcionario_id,))
        
        ultimos_registros = list(cursor.fetchall())
        
        return {
            'resumo_mensal': resumo_mensal,
            'ultimos_registros': ultimos_registros
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter resumo do funcionário: {e}")
        return {}

def obter_dashboard_geral(cursor):
    """Obtém dados para dashboard geral de administradores"""
    try:
        hoje = date.today()
        primeiro_dia = date(hoje.year, hoje.month, 1)
        
        # Estatísticas gerais
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT funcionario_id) as total_funcionarios,
                SUM(CASE WHEN saldo_liquido_minutos > 0 THEN 1 ELSE 0 END) as funcionarios_credito,
                SUM(CASE WHEN saldo_liquido_minutos < 0 THEN 1 ELSE 0 END) as funcionarios_devedor,
                SUM(saldo_liquido_minutos) as saldo_total_empresa
            FROM banco_horas
            WHERE data_referencia >= %s AND data_referencia <= %s
        """, (primeiro_dia, hoje))
        
        estatisticas = cursor.fetchone()
        
        # Top funcionários com mais atrasos
        cursor.execute("""
            SELECT 
                f.nome_completo,
                SUM(bh.atraso_entrada_minutos) as total_atrasos
            FROM banco_horas bh
            INNER JOIN funcionarios f ON bh.funcionario_id = f.id
            WHERE bh.data_referencia >= %s AND bh.data_referencia <= %s
            AND bh.atraso_entrada_minutos > 0
            GROUP BY bh.funcionario_id, f.nome_completo
            ORDER BY total_atrasos DESC
            LIMIT 5
        """, (primeiro_dia, hoje))
        
        top_atrasos = list(cursor.fetchall())
        
        # Justificativas pendentes
        cursor.execute("""
            SELECT COUNT(*) as total_pendentes
            FROM justificativas_ponto
            WHERE status_aprovacao = 'pendente'
        """)
        
        justificativas_pendentes = cursor.fetchone()['total_pendentes']
        
        return {
            'estatisticas': estatisticas,
            'top_atrasos': top_atrasos,
            'justificativas_pendentes': justificativas_pendentes
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter dashboard geral: {e}")
        return {}

def obter_relatorio_mensal(cursor, ano, mes, funcionario_id=None):
    """Obtém dados para relatório mensal"""
    try:
        primeiro_dia = date(ano, mes, 1)
        ultimo_dia = date(ano, mes, calendar.monthrange(ano, mes)[1])
        
        # Construir query base
        where_clause = "WHERE bh.data_referencia >= %s AND bh.data_referencia <= %s"
        params = [primeiro_dia, ultimo_dia]
        
        if funcionario_id:
            where_clause += " AND bh.funcionario_id = %s"
            params.append(funcionario_id)
        
        cursor.execute(f"""
            SELECT 
                f.nome_completo,
                bh.data_referencia,
                bh.atraso_entrada_minutos,
                bh.excesso_almoco_minutos,
                bh.saida_antecipada_minutos,
                bh.horas_extras_minutos,
                bh.saldo_liquido_minutos,
                bh.status_dia,
                bh.observacoes
            FROM banco_horas bh
            INNER JOIN funcionarios f ON bh.funcionario_id = f.id
            {where_clause}
            ORDER BY f.nome_completo, bh.data_referencia
        """, params)
        
        registros = list(cursor.fetchall())
        
        return registros
        
    except Exception as e:
        logger.error(f"Erro ao obter relatório mensal: {e}")
        return []
