{% extends "base.html" %}

{% block title %}Gestão de Clientes{% if empresa_principal %} - {{ empresa_principal.razao_social }}{% endif %}{% endblock %}

{% block extra_css %}
<style>
    /* ===== VARIÁVEIS CSS BASEADAS NO PADRÃO OFICIAL ===== */
    :root {
        --primary-color: #4fbdba;
        --primary-hover: #26a69a;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --border-color: #e5e7eb;
        --border-light: #f3f4f6;
        --success-color: #10b981;
        --success-bg: #dcfce7;
        --success-text: #166534;
        --warning-color: #f59e0b;
        --warning-bg: #fef3c7;
        --warning-text: #92400e;
        --danger-color: #ef4444;
        --danger-bg: #fee2e2;
        --danger-text: #dc2626;
        --info-color: #3b82f6;
        --info-bg: #dbeafe;
        --info-text: #1e40af;
    }

    /* ===== LAYOUT PRINCIPAL ===== */
    .main-container {
        background-color: var(--background-color);
        min-height: 100vh;
        padding: 2rem;
    }

    /* ===== HEADER MODERNO ===== */
    .modern-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .modern-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .header-content {
        position: relative;
        z-index: 2;
    }

    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .stat-card:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: block;
    }

    .stat-label {
        font-size: 0.875rem;
        opacity: 0.9;
        font-weight: 500;
    }

    /* ===== SEÇÃO DE AÇÕES ===== */
    .actions-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .section-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .section-title i {
        color: var(--primary-color);
    }

    .actions-group {
        display: flex;
        gap: 0.75rem;
    }

    /* ===== BOTÕES MODERNOS ===== */
    .modern-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        font-size: 0.875rem;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn-primary {
        background: var(--primary-color);
        color: white;
        border: 1px solid var(--primary-color);
    }

    .modern-btn-primary:hover {
        background: var(--primary-hover);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
    }

    .modern-btn-secondary {
        background: var(--card-background);
        color: var(--text-secondary);
        border: 1px solid var(--border-color);
    }

    .modern-btn-secondary:hover {
        background: var(--border-light);
        color: var(--text-primary);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* ===== FILTROS MODERNOS ===== */
    .filters-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin: 0;
    }

    .modern-input,
    .modern-select {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 0.875rem;
        background: var(--card-background);
        color: var(--text-primary);
        transition: all 0.3s ease;
        width: 100%;
    }

    .modern-input:focus,
    .modern-select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }

    .modern-input::placeholder {
        color: var(--text-muted);
    }

    /* ===== CARDS DE CLIENTES MODERNOS ===== */
    .clients-grid {
        display: grid;
        gap: 1.5rem;
    }

    .client-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .client-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-color);
        transform: scaleY(0);
        transition: transform 0.3s ease;
    }

    .client-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-color);
    }

    .client-card:hover::before {
        transform: scaleY(1);
    }

    .client-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .client-info {
        flex: 1;
    }

    .client-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 0.25rem 0;
    }

    .client-fantasy {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin: 0 0 0.5rem 0;
    }

    .client-cnpj {
        font-size: 0.75rem;
        color: var(--text-muted);
        font-family: 'Courier New', monospace;
        background: var(--border-light);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        display: inline-block;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .status-ativo {
        background: var(--success-bg);
        color: var(--success-text);
    }

    .status-pausado {
        background: var(--warning-bg);
        color: var(--warning-text);
    }

    .status-finalizado {
        background: var(--info-bg);
        color: var(--info-text);
    }

    .status-cancelado {
        background: var(--danger-bg);
        color: var(--danger-text);
    }

    .client-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin: 1rem 0;
    }

    .detail-group {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .detail-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .detail-value {
        font-size: 0.875rem;
        color: var(--text-primary);
        font-weight: 500;
    }

    .client-stats {
        display: flex;
        gap: 1rem;
        margin: 1rem 0;
        padding: 1rem;
        background: var(--border-light);
        border-radius: 8px;
    }

    .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
    }

    .stat-item-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .stat-item-label {
        font-size: 0.75rem;
        color: var(--text-muted);
        text-align: center;
        margin-top: 0.25rem;
    }

    .client-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
        text-decoration: none;
        border: 1px solid;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .action-btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .action-btn-primary:hover {
        background: var(--primary-hover);
        transform: translateY(-1px);
    }

    .action-btn-success {
        background: var(--success-color);
        color: white;
        border-color: var(--success-color);
    }

    .action-btn-success:hover {
        background: #059669;
        transform: translateY(-1px);
    }

    .action-btn-warning {
        background: var(--warning-color);
        color: white;
        border-color: var(--warning-color);
    }

    .action-btn-warning:hover {
        background: #d97706;
        transform: translateY(-1px);
    }

    .action-btn-secondary {
        background: var(--card-background);
        color: var(--text-secondary);
        border-color: var(--border-color);
    }

    .action-btn-secondary:hover {
        background: var(--border-light);
        color: var(--text-primary);
        transform: translateY(-1px);
    }

    /* ===== ESTADO VAZIO ===== */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        border-style: dashed;
    }

    .empty-icon {
        font-size: 4rem;
        color: var(--text-muted);
        margin-bottom: 1rem;
    }

    .empty-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
    }

    .empty-description {
        color: var(--text-muted);
        margin-bottom: 2rem;
    }

    /* ===== ANIMAÇÕES ===== */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .client-card {
        animation: fadeInUp 0.5s ease-out;
    }

    .client-card:nth-child(1) { animation-delay: 0.1s; }
    .client-card:nth-child(2) { animation-delay: 0.2s; }
    .client-card:nth-child(3) { animation-delay: 0.3s; }
    .client-card:nth-child(4) { animation-delay: 0.4s; }

    /* ===== RESPONSIVIDADE ===== */
    @media (max-width: 768px) {
        .main-container {
            padding: 1rem;
        }

        .modern-header {
            padding: 1.5rem;
        }

        .stats-container {
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .actions-section {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .actions-group {
            justify-content: center;
        }

        .filters-grid {
            grid-template-columns: 1fr;
        }

        .client-details {
            grid-template-columns: 1fr;
        }

        .client-stats {
            flex-direction: column;
            gap: 0.75rem;
        }

        .client-actions {
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .stats-container {
            grid-template-columns: 1fr;
        }

        .client-header {
            flex-direction: column;
            gap: 1rem;
        }

        .action-btn {
            flex: 1;
            justify-content: center;
        }
    }

    /* ===== MODAIS MODERNOS ===== */
    .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .modal-header {
        background: var(--border-light);
        border-bottom: 1px solid var(--border-color);
        border-radius: 12px 12px 0 0;
        padding: 1.5rem;
    }

    .modal-title {
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .modal-title i {
        color: var(--primary-color);
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        background: var(--border-light);
        border-top: 1px solid var(--border-color);
        border-radius: 0 0 12px 12px;
        padding: 1.5rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 0.875rem;
        background: var(--card-background);
        color: var(--text-primary);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Header Moderno -->
    <div class="modern-header">
        <div class="header-content">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 style="margin: 0; font-size: 2rem; font-weight: 700;">
                        <i class="fas fa-crown" style="margin-right: 0.75rem;"></i>
                        {% if empresa_principal %}{{ empresa_principal.razao_social }}{% else %}Empresa Principal{% endif %}
                    </h1>
                    <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 1rem;">Gestão de Empresas Clientes</p>
                </div>
                <div class="col-md-4">
                    <div class="stats-container">
                        <div class="stat-card">
                            <span class="stat-number">{{ stats.total_clientes }}</span>
                            <span class="stat-label">Total</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number">{{ stats.clientes_ativos }}</span>
                            <span class="stat-label">Ativos</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-number">{{ stats.funcionarios_alocados }}</span>
                            <span class="stat-label">Funcionários</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Seção de Ações -->
    <div class="actions-section">
        <h2 class="section-title">
            <i class="fas fa-building"></i>
            Clientes Cadastrados
        </h2>
        <div class="actions-group">
            <button class="modern-btn modern-btn-primary" onclick="mostrarModalAdicionarCliente()">
                <i class="fas fa-plus"></i>
                Adicionar Cliente
            </button>
            <a href="{{ url_for('empresa_principal.index') }}" class="modern-btn modern-btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Voltar ao Dashboard
            </a>
        </div>
    </div>

    <!-- Filtros Modernos -->
    <div class="filters-card">
        <div class="filters-grid">
            <div class="filter-group">
                <label class="filter-label">Status do Contrato</label>
                <select class="modern-select" id="filtroStatus" onchange="filtrarClientes()">
                    <option value="">Todos os status</option>
                    <option value="ativo">Ativo</option>
                    <option value="pausado">Pausado</option>
                    <option value="finalizado">Finalizado</option>
                    <option value="cancelado">Cancelado</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">Buscar por Nome</label>
                <input type="text" class="modern-input" id="buscarNome" placeholder="Digite o nome da empresa..." onkeyup="filtrarClientes()">
            </div>
            <div class="filter-group">
                <label class="filter-label">Ordenar por</label>
                <select class="modern-select" id="ordenacao" onchange="filtrarClientes()">
                    <option value="nome">Nome</option>
                    <option value="data_inicio">Data de Início</option>
                    <option value="funcionarios">Funcionários</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">&nbsp;</label>
                <button class="modern-btn modern-btn-secondary" onclick="limparFiltros()" style="width: 100%;">
                    <i class="fas fa-eraser"></i>
                    Limpar Filtros
                </button>
            </div>
        </div>
    </div>

    <!-- Lista de Clientes -->
    <div id="listaClientes" class="clients-grid">
        {% if clientes %}
            {% for cliente in clientes %}
            <div class="client-card" data-status="{{ cliente.status_contrato }}" data-nome="{{ cliente.razao_social.lower() }}">
                <div class="client-header">
                    <div class="client-info">
                        <h3 class="client-name">{{ cliente.razao_social }}</h3>
                        {% if cliente.nome_fantasia %}
                            <p class="client-fantasy">{{ cliente.nome_fantasia }}</p>
                        {% endif %}
                        <span class="client-cnpj">{{ cliente.cnpj }}</span>
                    </div>
                    <div>
                        <span class="status-badge status-{{ cliente.status_contrato }}">
                            {{ cliente.status_contrato.title() }}
                        </span>
                    </div>
                </div>

                <div class="client-details">
                    {% if cliente.nome_contrato %}
                    <div class="detail-group">
                        <span class="detail-label">Contrato</span>
                        <span class="detail-value">{{ cliente.nome_contrato }}</span>
                    </div>
                    {% endif %}
                    {% if cliente.codigo_contrato %}
                    <div class="detail-group">
                        <span class="detail-label">Código</span>
                        <span class="detail-value">{{ cliente.codigo_contrato }}</span>
                    </div>
                    {% endif %}
                    <div class="detail-group">
                        <span class="detail-label">Início</span>
                        <span class="detail-value">{{ cliente.data_inicio.strftime('%d/%m/%Y') if cliente.data_inicio else 'N/A' }}</span>
                    </div>
                    {% if cliente.data_fim %}
                    <div class="detail-group">
                        <span class="detail-label">Fim</span>
                        <span class="detail-value">{{ cliente.data_fim.strftime('%d/%m/%Y') }}</span>
                    </div>
                    {% endif %}
                </div>

                <div class="client-stats">
                    <div class="stat-item">
                        <span class="stat-item-number">{{ cliente.funcionarios_alocados }}</span>
                        <span class="stat-item-label">Funcionários<br>Alocados</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-item-number">{{ cliente.jornadas_disponiveis }}</span>
                        <span class="stat-item-label">Jornadas<br>Disponíveis</span>
                    </div>
                </div>

                <div class="client-actions">
                    <button class="action-btn action-btn-primary" onclick="verDetalhesCliente({{ cliente.id }})">
                        <i class="fas fa-eye"></i>
                        Detalhes
                    </button>
                    <button class="action-btn action-btn-success" onclick="alocarFuncionario({{ cliente.empresa_cliente_id }})">
                        <i class="fas fa-user-plus"></i>
                        Alocar
                    </button>
                    <button class="action-btn action-btn-warning" onclick="editarCliente({{ cliente.id }})">
                        <i class="fas fa-edit"></i>
                        Editar
                    </button>
                    {% if cliente.status_contrato == 'ativo' %}
                        <button class="action-btn action-btn-secondary" onclick="pausarContrato({{ cliente.id }})">
                            <i class="fas fa-pause"></i>
                            Pausar
                        </button>
                    {% elif cliente.status_contrato == 'pausado' %}
                        <button class="action-btn action-btn-success" onclick="reativarContrato({{ cliente.id }})">
                            <i class="fas fa-play"></i>
                            Reativar
                        </button>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-building empty-icon"></i>
                <h3 class="empty-title">Nenhum cliente cadastrado</h3>
                <p class="empty-description">Use o botão "Adicionar Cliente" acima para começar a gerenciar contratos e funcionários.</p>
                <button class="modern-btn modern-btn-primary" onclick="mostrarModalAdicionarCliente()">
                    <i class="fas fa-plus"></i>
                    Adicionar Primeiro Cliente
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal Adicionar Cliente -->
<div class="modal fade" id="modalAdicionarCliente" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Adicionar Novo Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formAdicionarCliente" onsubmit="adicionarCliente(event)">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Empresa Cliente *</label>
                                <select class="form-control" name="empresa_cliente_id" required>
                                    <option value="">Selecione uma empresa...</option>
                                    {% for empresa in empresas_disponiveis %}
                                        <option value="{{ empresa.id }}">{{ empresa.razao_social }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nome do Contrato</label>
                                <input type="text" class="form-control" name="nome_contrato" placeholder="Ex: Prestação de Serviços 2025">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Código do Contrato</label>
                                <input type="text" class="form-control" name="codigo_contrato" placeholder="Ex: CONT-2025-001">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Valor do Contrato</label>
                                <input type="number" class="form-control" name="valor_contrato" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Início *</label>
                                <input type="date" class="form-control" name="data_inicio" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Fim</label>
                                <input type="date" class="form-control" name="data_fim">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Descrição do Projeto</label>
                        <textarea class="form-control" name="descricao_projeto" rows="3" placeholder="Descreva o projeto ou serviços a serem prestados..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" name="observacoes" rows="2" placeholder="Observações adicionais..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modern-btn modern-btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i> Adicionar Cliente
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Alocar Funcionário -->
<div class="modal fade" id="modalAlocarFuncionario" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-plus"></i> Alocar Funcionário</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formAlocarFuncionario" onsubmit="confirmarAlocacao(event)">
                <div class="modal-body">
                    <input type="hidden" name="empresa_cliente_id" id="empresaClienteId">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Funcionário *</label>
                                <select class="form-control" name="funcionario_id" id="funcionarioSelect" required>
                                    <option value="">Carregando funcionários...</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Jornada de Trabalho *</label>
                                <select class="form-control" name="jornada_trabalho_id" id="jornadaSelect" required>
                                    <option value="">Carregando jornadas...</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Cargo no Cliente</label>
                                <input type="text" class="form-control" name="cargo_no_cliente" placeholder="Ex: Analista de Sistemas">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Percentual de Alocação (%)</label>
                                <input type="number" class="form-control" name="percentual_alocacao" value="100" min="1" max="100">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Início *</label>
                                <input type="date" class="form-control" name="data_inicio" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Fim</label>
                                <input type="date" class="form-control" name="data_fim">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Valor da Hora (R$)</label>
                        <input type="number" class="form-control" name="valor_hora" step="0.01" placeholder="0.00">
                    </div>

                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" name="observacoes" rows="2" placeholder="Observações sobre a alocação..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modern-btn modern-btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-user-plus"></i> Alocar Funcionário
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detalhes do Cliente -->
<div class="modal fade" id="modalDetalhesCliente" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-building"></i> Detalhes do Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="detalhesClienteContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p>Carregando detalhes...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Editar Cliente -->
<div class="modal fade" id="modalEditarCliente" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Editar Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="formEditarCliente" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Empresa Cliente *</label>
                                <input type="text" class="form-control" id="editEmpresaNome" readonly>
                                <input type="hidden" id="editClienteId" name="cliente_id">
                                <input type="hidden" id="editEmpresaClienteId" name="empresa_cliente_id">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nome do Contrato</label>
                                <input type="text" class="form-control" id="editNomeContrato" name="nome_contrato" placeholder="Ex: Prestação de Serviços 2025">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Código do Contrato</label>
                                <input type="text" class="form-control" id="editCodigoContrato" name="codigo_contrato" placeholder="Ex: CONT-2025-001">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Valor do Contrato</label>
                                <input type="number" class="form-control" id="editValorContrato" name="valor_contrato" step="0.01" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Início *</label>
                                <input type="date" class="form-control" id="editDataInicio" name="data_inicio" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Data de Fim</label>
                                <input type="date" class="form-control" id="editDataFim" name="data_fim">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Status do Contrato</label>
                                <select class="form-control" id="editStatusContrato" name="status_contrato">
                                    <option value="ativo">Ativo</option>
                                    <option value="pausado">Pausado</option>
                                    <option value="finalizado">Finalizado</option>
                                    <option value="cancelado">Cancelado</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Descrição do Projeto</label>
                        <textarea class="form-control" id="editDescricaoProjeto" name="descricao_projeto" rows="3" placeholder="Descreva o projeto ou serviços a serem prestados..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Observações</label>
                        <textarea class="form-control" id="editObservacoes" name="observacoes" rows="3" placeholder="Observações adicionais..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modern-btn modern-btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="modern-btn modern-btn-warning">
                        <i class="fas fa-save"></i> Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function mostrarModalAdicionarCliente() {
    const modal = new bootstrap.Modal(document.getElementById('modalAdicionarCliente'));
    modal.show();
}

function adicionarCliente(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    
    fetch('{{ url_for("empresa_principal.adicionar_cliente_route") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAdicionarCliente'));
            modal.hide();
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao adicionar cliente');
    });
}

function filtrarClientes() {
    const status = document.getElementById('filtroStatus').value.toLowerCase();
    const nome = document.getElementById('buscarNome').value.toLowerCase();
    const cards = document.querySelectorAll('.client-card');
    
    cards.forEach(card => {
        const cardStatus = card.getAttribute('data-status');
        const cardNome = card.getAttribute('data-nome');
        
        const statusMatch = !status || cardStatus === status;
        const nomeMatch = !nome || cardNome.includes(nome);
        
        if (statusMatch && nomeMatch) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function limparFiltros() {
    document.getElementById('filtroStatus').value = '';
    document.getElementById('buscarNome').value = '';
    document.getElementById('ordenacao').value = 'nome';
    filtrarClientes();
}

function verDetalhesCliente(clienteId) {
    const modal = new bootstrap.Modal(document.getElementById('modalDetalhesCliente'));
    modal.show();

    fetch(`{{ url_for("empresa_principal.detalhes_cliente", cliente_id=0) }}`.replace('0', clienteId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('detalhesClienteContent').innerHTML = data.html;
            } else {
                document.getElementById('detalhesClienteContent').innerHTML =
                    '<div class="alert alert-danger">Erro ao carregar detalhes: ' + data.message + '</div>';
            }
        })
        .catch(error => {
            document.getElementById('detalhesClienteContent').innerHTML =
                '<div class="alert alert-danger">Erro ao carregar detalhes</div>';
        });
}

function alocarFuncionario(empresaId) {
    document.getElementById('empresaClienteId').value = empresaId;

    // Carregar funcionários disponíveis
    fetch('{{ url_for("empresa_principal.get_funcionarios_disponiveis_api") }}')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('funcionarioSelect');
            select.innerHTML = '<option value="">Selecione um funcionário...</option>';

            data.funcionarios.forEach(func => {
                const option = document.createElement('option');
                option.value = func.id;
                option.textContent = `${func.nome_completo} - ${func.cargo}`;
                select.appendChild(option);
            });
        });

    // Carregar jornadas disponíveis
    fetch('{{ url_for("empresa_principal.get_jornadas_disponiveis") }}')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('jornadaSelect');
            select.innerHTML = '<option value="">Selecione uma jornada...</option>';

            data.jornadas.forEach(jornada => {
                const option = document.createElement('option');
                option.value = jornada.id;
                option.textContent = `${jornada.nome} (${jornada.empresa_nome}) - ${jornada.carga_horaria}`;
                select.appendChild(option);
            });
        });

    const modal = new bootstrap.Modal(document.getElementById('modalAlocarFuncionario'));
    modal.show();
}

function editarCliente(clienteId) {
    // Buscar dados do cliente
    fetch(`{{ url_for("empresa_principal.get_cliente_dados", cliente_id=0) }}`.replace('0', clienteId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const cliente = data.cliente;

                // Preencher o formulário
                document.getElementById('editClienteId').value = clienteId;
                document.getElementById('editEmpresaClienteId').value = cliente.empresa_cliente_id;
                document.getElementById('editEmpresaNome').value = cliente.razao_social;
                document.getElementById('editNomeContrato').value = cliente.nome_contrato || '';
                document.getElementById('editCodigoContrato').value = cliente.codigo_contrato || '';
                document.getElementById('editValorContrato').value = cliente.valor_contrato || '';
                document.getElementById('editDataInicio').value = cliente.data_inicio || '';
                document.getElementById('editDataFim').value = cliente.data_fim || '';
                document.getElementById('editStatusContrato').value = cliente.status_contrato || 'ativo';
                document.getElementById('editDescricaoProjeto').value = cliente.descricao_projeto || '';
                document.getElementById('editObservacoes').value = cliente.observacoes || '';

                // Mostrar modal
                const modal = new bootstrap.Modal(document.getElementById('modalEditarCliente'));
                modal.show();
            } else {
                alert('Erro ao carregar dados do cliente: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao carregar dados do cliente');
        });
}

function pausarContrato(clienteId) {
    // Primeiro, verificar o status atual
    fetch(`{{ url_for("empresa_principal.get_cliente_dados", cliente_id=0) }}`.replace('0', clienteId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const statusAtual = data.cliente.status_contrato;
                let novoStatus, mensagem, acao;

                if (statusAtual === 'ativo') {
                    novoStatus = 'pausado';
                    mensagem = 'Deseja pausar este contrato?';
                    acao = 'pausar';
                } else if (statusAtual === 'pausado') {
                    novoStatus = 'ativo';
                    mensagem = 'Deseja reativar este contrato?';
                    acao = 'reativar';
                } else {
                    alert('Este contrato não pode ser pausado/reativado. Status atual: ' + statusAtual);
                    return;
                }

                if (confirm(mensagem)) {
                    alterarStatusContrato(clienteId, novoStatus);
                }
            } else {
                alert('Erro ao verificar status do contrato');
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao verificar status do contrato');
        });
}

function reativarContrato(clienteId) {
    if (confirm('Deseja reativar este contrato?')) {
        alterarStatusContrato(clienteId, 'ativo');
    }
}

function confirmarAlocacao(event) {
    event.preventDefault();

    const formData = new FormData(event.target);

    fetch('{{ url_for("empresa_principal.alocar_funcionario_route") }}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAlocarFuncionario'));
            modal.hide();
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao alocar funcionário');
    });
}

function alterarStatusContrato(clienteId, novoStatus) {
    fetch(`{{ url_for("empresa_principal.alterar_status_contrato", cliente_id=0) }}`.replace('0', clienteId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: novoStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            filtrarClientes(); // Recarregar lista
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao alterar status do contrato');
    });
}

function editarClienteSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const clienteId = formData.get('cliente_id');

    fetch(`{{ url_for("empresa_principal.editar_cliente", cliente_id=0) }}`.replace('0', clienteId), {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Cliente editado com sucesso!');
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalEditarCliente'));
            modal.hide();
            filtrarClientes(); // Recarregar lista
        } else {
            alert('Erro ao editar cliente: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao editar cliente');
    });
}

// Event listeners e Animações
document.addEventListener('DOMContentLoaded', function() {
    // Event listeners
    if (document.getElementById('formAdicionarCliente')) {
        document.getElementById('formAdicionarCliente').addEventListener('submit', adicionarCliente);
    }
    if (document.getElementById('formAlocarFuncionario')) {
        document.getElementById('formAlocarFuncionario').addEventListener('submit', confirmarAlocacao);
    }
    if (document.getElementById('formEditarCliente')) {
        document.getElementById('formEditarCliente').addEventListener('submit', editarClienteSubmit);
    }

    // Carregar clientes ao carregar a página
    filtrarClientes();

    // Animações
    const cards = document.querySelectorAll('.client-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
