# IDENTIDADE VISUAL - RLPONTO-WEB

**Data de Criação:** 07/07/2025  
**Versão:** 1.0  
**Sistema:** RLPONTO-WEB v1.0  
**Desenvolvido por:** AiNexus Tecnologia  
**Objetivo:** Estabelecer padrões visuais obrigatórios para garantir consistência em toda a aplicação

---

## 📋 Índice

1. [Paleta de Cores Oficial](#paleta-de-cores-oficial)
2. [Tipografia e Fontes](#tipografia-e-fontes)
3. [Layout e Estrutura](#layout-e-estrutura)
4. [Componentes Padrão](#componentes-padrão)
5. [Estados Visuais](#estados-visuais)
6. [Responsividade](#responsividade)
7. [Regras Obrigatórias](#regras-obrigatórias)
8. [Exemplos de Implementação](#exemplos-de-implementação)

---

## 🎨 Paleta de Cores Oficial

### Cores Principais
```css
:root {
    /* Cor primária - Verde-azulado (igual sidebar) */
    --primary-color: #4fbdba;
    --primary-hover: #26a69a;
    --primary-light: #80cbc4;
    --primary-dark: #00695c;
    
    /* Backgrounds */
    --background-color: #f9fafb;        /* Background principal */
    --card-background: #ffffff;         /* Fundo de cards */
    --hover-bg: #f3f4f6;               /* Hover states */
    --sidebar-bg: #ffffff;             /* Sidebar background */
    
    /* Textos */
    --text-primary: #1f2937;           /* Texto principal (preto) */
    --text-secondary: #6b7280;         /* Texto secundário (cinza médio) */
    --text-muted: #9ca3af;             /* Texto desabilitado */
    --text-white: #ffffff;             /* Texto branco */
    
    /* Bordas e divisores */
    --border-color: #e5e7eb;           /* Bordas padrão */
    --border-light: #f3f4f6;           /* Bordas claras */
    --border-dark: #d1d5db;            /* Bordas escuras */
    
    /* Estados */
    --success-color: #10b981;          /* Verde sucesso */
    --success-bg: #dcfce7;             /* Background sucesso */
    --success-text: #166534;           /* Texto sucesso */
    
    --warning-color: #f59e0b;          /* Amarelo aviso */
    --warning-bg: #fef3c7;             /* Background aviso */
    --warning-text: #92400e;           /* Texto aviso */
    
    --danger-color: #ef4444;           /* Vermelho erro */
    --danger-bg: #fee2e2;              /* Background erro */
    --danger-text: #dc2626;            /* Texto erro */
    
    --info-color: #3b82f6;             /* Azul informação */
    --info-bg: #dbeafe;                /* Background info */
    --info-text: #1e40af;              /* Texto info */
}
```

### ❌ Cores Proibidas
- **Backgrounds escuros:** #1f2937, #111827, #0f172a
- **Gradientes excessivos:** Múltiplos gradientes coloridos
- **Cores muito vibrantes:** #ff0000, #00ff00, #ff00ff
- **Contrastes baixos:** Texto claro sobre fundo claro

---

## 📝 Tipografia e Fontes

### Hierarquia de Fontes
```css
/* Família de fontes padrão */
font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

/* Tamanhos obrigatórios */
--font-size-xs: 0.75rem;      /* 12px - Textos muito pequenos */
--font-size-sm: 0.875rem;     /* 14px - Textos pequenos */
--font-size-base: 1rem;       /* 16px - Texto base */
--font-size-lg: 1.125rem;     /* 18px - Textos grandes */
--font-size-xl: 1.25rem;      /* 20px - Subtítulos */
--font-size-2xl: 1.5rem;      /* 24px - Títulos */
--font-size-3xl: 1.875rem;    /* 30px - Títulos principais */

/* Pesos de fonte */
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;

/* Alturas de linha */
--line-height-tight: 1.25;
--line-height-normal: 1.5;
--line-height-relaxed: 1.75;
```

### Aplicação por Elemento
- **Títulos H1:** 30px, peso 700, cor --text-primary
- **Títulos H2:** 24px, peso 600, cor --text-primary
- **Títulos H3:** 20px, peso 600, cor --text-primary
- **Texto base:** 16px, peso 400, cor --text-primary
- **Texto secundário:** 14px, peso 400, cor --text-secondary
- **Texto pequeno:** 12px, peso 400, cor --text-muted

---

## 📐 Layout e Estrutura

### Container Principal
```css
.main-container {
    background-color: var(--background-color);
    min-height: 100vh;
    padding: 2rem;
}

@media (max-width: 768px) {
    .main-container {
        padding: 1rem;
    }
}
```

### Grid System
```css
/* Grid responsivo padrão */
.grid-auto {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}
```

### Espaçamentos Padrão
```css
/* Margens e paddings obrigatórios */
--spacing-xs: 0.25rem;    /* 4px */
--spacing-sm: 0.5rem;     /* 8px */
--spacing-md: 1rem;       /* 16px */
--spacing-lg: 1.5rem;     /* 24px */
--spacing-xl: 2rem;       /* 32px */
--spacing-2xl: 3rem;      /* 48px */
```

---

## 🧩 Componentes Padrão

### Cards Obrigatórios
```css
.standard-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 
                0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.standard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-color: var(--border-dark);
}
```

### Botões Padrão
```css
.btn-primary {
    background: var(--primary-color);
    border: 1px solid var(--primary-color);
    color: var(--text-white);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--hover-bg);
    border-color: var(--border-dark);
}
```

### Formulários Padrão
```css
.form-control {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    background: var(--card-background);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: 0.5rem;
    display: block;
}
```

### Headers Padrão
```css
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: var(--text-white);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 60%;
    height: 200%;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: rotate(15deg);
}
```

---

## 🎯 Estados Visuais

### Estados de Sucesso
```css
.status-success {
    background: var(--success-bg);
    color: var(--success-text);
    border: 1px solid var(--success-color);
    border-radius: 6px;
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-sm);
    font-weight: 600;
}
```

### Estados de Aviso
```css
.status-warning {
    background: var(--warning-bg);
    color: var(--warning-text);
    border: 1px solid var(--warning-color);
    border-radius: 6px;
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-sm);
    font-weight: 600;
}
```

### Estados de Erro
```css
.status-danger {
    background: var(--danger-bg);
    color: var(--danger-text);
    border: 1px solid var(--danger-color);
    border-radius: 6px;
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-sm);
    font-weight: 600;
}
```

### Estados de Informação
```css
.status-info {
    background: var(--info-bg);
    color: var(--info-text);
    border: 1px solid var(--info-color);
    border-radius: 6px;
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-sm);
    font-weight: 600;
}
```

---

## 📱 Responsividade

### Breakpoints Obrigatórios
```css
/* Mobile First Approach */
/* Mobile: < 768px */
/* Tablet: 768px - 1024px */
/* Desktop: > 1024px */

@media (max-width: 767px) {
    .main-container {
        padding: 1rem;
    }
    
    .grid-auto,
    .grid-2,
    .grid-3 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .page-header {
        padding: 1.5rem;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .grid-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}
```

### Regras de Adaptação
- **Mobile:** 1 coluna sempre, padding reduzido
- **Tablet:** 2 colunas máximo, espaçamentos médios
- **Desktop:** 3+ colunas permitidas, espaçamentos completos

---

## ⚠️ Regras Obrigatórias

### ✅ SEMPRE FAZER
1. **Background principal:** Sempre usar `var(--background-color)` (#f9fafb)
2. **Cards:** Sempre fundo branco com bordas sutis
3. **Texto:** Sempre escuro sobre fundo claro para legibilidade
4. **Cor primária:** Sempre #4fbdba para elementos de destaque
5. **Hover effects:** Sempre suaves com `translateY(-2px)`
6. **Border radius:** Sempre 8px-12px para consistência
7. **Transições:** Sempre 0.3s ease ou cubic-bezier
8. **Mobile first:** Sempre desenvolver responsivo

### ❌ NUNCA FAZER
1. **Backgrounds escuros:** Nunca usar tons escuros como fundo principal
2. **Texto claro sobre claro:** Nunca comprometer legibilidade
3. **Cores conflitantes:** Nunca usar cores fora da paleta oficial
4. **Gradientes excessivos:** Nunca mais de 1 gradiente por tela
5. **Animações bruscas:** Nunca transições menores que 0.2s
6. **Componentes inconsistentes:** Nunca criar novos padrões sem documentar
7. **Breakpoints customizados:** Nunca usar breakpoints fora do padrão

### 🔄 Processo de Validação
Antes de implementar qualquer visual:
1. ✅ Verificar se segue a paleta de cores oficial
2. ✅ Confirmar legibilidade em todos os estados
3. ✅ Testar responsividade nos 3 breakpoints
4. ✅ Validar consistência com componentes existentes
5. ✅ Documentar qualquer novo padrão criado

---

## 💡 Exemplos de Implementação

### Dashboard Principal
```html
<div class="main-container">
    <!-- Header -->
    <div class="page-header">
        <h1>Título da Página</h1>
        <p>Descrição opcional</p>
    </div>
    
    <!-- Grid de Cards -->
    <div class="grid-auto">
        <div class="standard-card">
            <h3>Card Title</h3>
            <p>Card content</p>
        </div>
    </div>
    
    <!-- Botões de Ação -->
    <div class="mt-4">
        <button class="btn-primary">Ação Principal</button>
        <button class="btn-secondary">Ação Secundária</button>
    </div>
</div>
```

### Formulário Padrão
```html
<form class="standard-card">
    <div class="mb-3">
        <label class="form-label">Nome</label>
        <input type="text" class="form-control" placeholder="Digite o nome">
    </div>
    
    <div class="mb-3">
        <label class="form-label">Status</label>
        <select class="form-control">
            <option>Ativo</option>
            <option>Inativo</option>
        </select>
    </div>
    
    <div class="d-flex gap-2">
        <button type="submit" class="btn-primary">Salvar</button>
        <button type="button" class="btn-secondary">Cancelar</button>
    </div>
</form>
```

### Estados de Status
```html
<div class="d-flex gap-2">
    <span class="status-success">Ativo</span>
    <span class="status-warning">Pendente</span>
    <span class="status-danger">Inativo</span>
    <span class="status-info">Informação</span>
</div>
```

---

## 🔄 Versionamento

### Histórico de Versões
- **v1.0 (07/07/2025):** Versão inicial baseada na correção do dashboard
- **Próximas versões:** Atualizações baseadas em feedback e melhorias

### Processo de Atualização
1. Toda mudança visual deve ser documentada aqui
2. Versão deve ser incrementada a cada mudança significativa
3. Changelog deve ser mantido atualizado
4. Testes de regressão visual obrigatórios

---

## 🎯 Conclusão

Este documento é a **FONTE ÚNICA DA VERDADE** para decisões visuais no projeto RLPONTO-WEB. 

### Para IAs e Desenvolvedores:
- **SEMPRE consultar** este documento antes de implementar qualquer visual
- **NUNCA desviar** da paleta de cores oficial
- **SEMPRE priorizar** legibilidade e consistência
- **SEMPRE testar** responsividade

### Compromisso de Qualidade:
- Background claro (#f9fafb) sempre
- Cards brancos com bordas sutis sempre  
- Texto escuro (#1f2937) para legibilidade sempre
- Cor primária #4fbdba para destaques sempre
- Hover effects suaves sempre
- Responsividade mobile-first sempre

**📊 DOCUMENTO CRIADO EM:** 07/07/2025  
**🔄 PRÓXIMA REVISÃO:** 07/10/2025  
**🏢 EMPRESA DESENVOLVEDORA:** AiNexus Tecnologia  
**👨‍💻 DESENVOLVEDOR:** Richardson Rodrigues - Full Stack Developer  
**🎯 SISTEMA:** RLPONTO-WEB v1.0 - Sistema de Controle de Ponto Biométrico Empresarial  
**© COPYRIGHT:** 2025 AiNexus Tecnologia. Todos os direitos reservados. 