{% extends "base.html" %}

{% block title %}Detalhes do Funcionário - {{ funcionario.nome_completo }}{% endblock %}

{% block extra_css %}
<style>
/* 🎨 MODERN EMPLOYEE DETAILS - Inspirado no MCP @21st-dev/magic */

:root {
    --primary-color: #4fbdba;
    --secondary-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --gray-50: #fafbfc;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
}

/* Global Styles */
body {
    background: var(--gray-50);
    font-family: 'SF Mono', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Modern Breadcrumbs */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 2rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.breadcrumb-item a:hover {
    color: var(--gray-800);
    transform: translateX(2px);
}

.breadcrumb-item.active {
    color: var(--gray-600);
    font-weight: 600;
}

/* Hero Profile Section */
.profile-hero {
    background: var(--white);
    border-radius: 24px;
    padding: 0;
    border: 1px solid var(--gray-200);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.profile-hero:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
}

.hero-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
}

/* Modern Profile Image */
.profile-image-container {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 24px;
    overflow: hidden;
    border: 4px solid var(--gray-100);
    transition: all 0.3s ease;
    background: var(--gray-100);
}

.profile-image-container:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.profile-image-container:hover .profile-image {
    transform: scale(1.1);
}

/* Profile Info */
.profile-info h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.profile-subtitle {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: 1rem;
    font-weight: 500;
}

.profile-badges {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.modern-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-primary {
    background: rgba(79, 189, 186, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(79, 189, 186, 0.2);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 1rem;
    margin-left: auto;
}

.modern-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #3da8a6;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-200);
}

.btn-secondary:hover {
    background: var(--gray-200);
    transform: translateY(-1px);
}

/* Modern Cards */
.modern-card {
    background: var(--white);
    border-radius: 20px;
    border: 1px solid var(--gray-200);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.modern-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.card-header-modern {
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
}

.card-title-modern {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-title-modern i {
    color: var(--primary-color);
    font-size: 1.125rem;
}

.card-body-modern {
    padding: 2rem;
}

/* Field Groups */
.field-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.field-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.field-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.field-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    line-height: 1.4;
}

.field-value.monospace {
    font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    background: var(--gray-50);
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    border: 1px solid var(--gray-200);
    font-size: 0.875rem;
}

.field-value a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.2s ease;
}

.field-value a:hover {
    color: var(--gray-900);
    transform: translateX(2px);
}

/* Schedule Cards */
.schedule-item {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.schedule-item:hover {
    background: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.schedule-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.schedule-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.time-slot {
    text-align: center;
}

.time-label {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.time-value {
    font-family: 'SF Mono', monospace;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-color);
}

.schedule-arrow {
    color: var(--primary-color);
    font-size: 1.25rem;
}

/* EPI Section */
.epi-item {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.epi-item:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
    transform: translateX(4px);
}

.epi-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    gap: 1rem;
}

.epi-info h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.epi-ca {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
}

.epi-status {
    margin-left: auto;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
}

.status-expired {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-valid {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-no-date {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
}

.epi-delivery {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.5rem;
}

.epi-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--gray-500);
}

.epi-empty i {
    font-size: 3rem;
    color: var(--gray-300);
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .action-buttons {
        margin-left: 0;
        justify-content: center;
    }
    
         .profile-info h1 {
         font-size: 1.5rem;
     }
    
    .field-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-card {
    animation: fadeInUp 0.6s ease-out;
}

.modern-card:nth-child(2) { animation-delay: 0.1s; }
.modern-card:nth-child(3) { animation-delay: 0.2s; }
.modern-card:nth-child(4) { animation-delay: 0.3s; }

</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- Modern Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ url_for('index') }}">
                    <i class="fas fa-home"></i> Início
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url_for('funcionarios.index') }}">Funcionários</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">{{ funcionario.nome_completo }}</li>
        </ol>
    </nav>

    <!-- Modern Profile Hero -->
    <div class="profile-hero">
        <div class="hero-content">
                         <!-- Modern Profile Image -->
             <div class="profile-image-container">
                                  <img src="{{ url_for('funcionarios.foto_funcionario', funcionario_id=funcionario.id) }}" 
                      alt="Foto de {{ funcionario.nome_completo }}" 
                      class="profile-image"
                       onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNDUiIHI9IjE1IiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik02MCA2NkMzNy45IDY2IDIwIDc4LjMgMjAgOTNWMTEwSDEwMFY5M0MxMDAgNzguMyA4Mi4xIDY2IDYwIDY2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'">
             </div>
            
            <!-- Profile Information -->
            <div class="profile-info flex-grow-1">
                <h1>{{ funcionario.nome_completo }}</h1>
                <p class="profile-subtitle">{{ funcionario.cargo }} • {{ funcionario.setor_obra }}</p>
                
                <div class="profile-badges">
                    {% if funcionario.status_cadastro == 'Ativo' %}
                        <span class="modern-badge badge-success">
                            <i class="fas fa-check-circle"></i>
                            {{ funcionario.status_cadastro }}
                        </span>
                    {% else %}
                        <span class="modern-badge badge-danger">
                            <i class="fas fa-times-circle"></i>
                            {{ funcionario.status_cadastro }}
                        </span>
                    {% endif %}
                    <span class="modern-badge badge-primary">
                        <i class="fas fa-id-card"></i>
                        {{ funcionario.matricula_empresa }}
                    </span>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                {% if current_user.is_admin %}
                <a href="{{ url_for('funcionarios.editar', funcionario_id=funcionario.id) }}" 
                   class="modern-btn btn-primary">
                    <i class="fas fa-edit"></i> Editar
                </a>
                {% endif %}
                <a href="{{ url_for('funcionarios.index') }}" 
                   class="modern-btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>
        </div>
    </div>

    <!-- Information Cards Grid -->
    <div class="row g-4">
        <!-- Personal Information -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-user"></i>
                        Informações Pessoais
                    </h5>
                </div>
                <div class="card-body-modern">
                    <div class="field-grid">
                        <div class="field-item">
                            <div class="field-label">Nome Completo</div>
                            <div class="field-value">{{ funcionario.nome_completo }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">CPF</div>
                            <div class="field-value monospace">{{ funcionario.cpf | format_cpf }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">RG</div>
                            <div class="field-value">{{ funcionario.rg or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Data de Nascimento</div>
                            <div class="field-value">{{ funcionario.data_nascimento | format_date }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Sexo</div>
                            <div class="field-value">{{ funcionario.sexo }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Estado Civil</div>
                            <div class="field-value">{{ funcionario.estado_civil or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Nacionalidade</div>
                            <div class="field-value">{{ funcionario.nacionalidade or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Telefone</div>
                            <div class="field-value">
                                {% if funcionario.telefone1 %}
                                    <a href="tel:{{ funcionario.telefone1 }}">
                                        {{ funcionario.telefone1 | format_telefone }}
                                    </a>
                                {% else %}
                                    Não informado
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contractual Information -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-briefcase"></i>
                        Informações Contratuais
                    </h5>
                </div>
                <div class="card-body-modern">
                    <div class="field-grid">
                        <div class="field-item">
                            <div class="field-label">Matrícula</div>
                            <div class="field-value monospace">{{ funcionario.matricula_empresa }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Status</div>
                            <div class="field-value">
                                {% if funcionario.status_cadastro == 'Ativo' %}
                                    <span class="modern-badge badge-success">{{ funcionario.status_cadastro }}</span>
                                {% elif funcionario.status_cadastro == 'Inativo' %}
                                    <span class="modern-badge badge-danger">{{ funcionario.status_cadastro }}</span>
                                {% else %}
                                    <span class="modern-badge badge-primary">{{ funcionario.status_cadastro }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Cargo</div>
                            <div class="field-value">{{ funcionario.cargo }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Setor</div>
                            <div class="field-value">{{ funcionario.setor_obra }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Data de Admissão</div>
                            <div class="field-value">{{ funcionario.data_admissao | format_date }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Tipo de Contrato</div>
                            <div class="field-value">{{ funcionario.tipo_contrato or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">CTPS</div>
                            <div class="field-value monospace">{{ funcionario.ctps_numero or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">PIS/PASEP</div>
                            <div class="field-value monospace">{{ funcionario.pis_pasep or 'Não informado' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Work Schedule -->
        <div class="col-lg-8">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-clock"></i>
                        Jornada de Trabalho
                    </h5>
                </div>
                <div class="card-body-modern">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="schedule-item">
                                <div class="schedule-title">Segunda a Quinta-feira</div>
                                <div class="schedule-time">
                                    <div class="time-slot">
                                        <div class="time-label">Entrada</div>
                                        <div class="time-value">{{ funcionario.jornada_seg_qui_entrada or '--:--' }}</div>
                                    </div>
                                    <i class="fas fa-arrow-right schedule-arrow"></i>
                                    <div class="time-slot">
                                        <div class="time-label">Saída</div>
                                        <div class="time-value">{{ funcionario.jornada_seg_qui_saida or '--:--' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="schedule-item">
                                <div class="schedule-title">Sexta-feira</div>
                                <div class="schedule-time">
                                    <div class="time-slot">
                                        <div class="time-label">Entrada</div>
                                        <div class="time-value">{{ funcionario.jornada_sex_entrada or '--:--' }}</div>
                                    </div>
                                    <i class="fas fa-arrow-right schedule-arrow"></i>
                                    <div class="time-slot">
                                        <div class="time-label">Saída</div>
                                        <div class="time-value">{{ funcionario.jornada_sex_saida or '--:--' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="schedule-item">
                                <div class="schedule-title">Intervalo para Almoço</div>
                                <div class="schedule-time">
                                    <div class="time-slot">
                                        <div class="time-label">Saída</div>
                                        <div class="time-value">{{ funcionario.jornada_intervalo_entrada or '--:--' }}</div>
                                    </div>
                                    <i class="fas fa-arrow-right schedule-arrow"></i>
                                    <div class="time-slot">
                                        <div class="time-label">Retorno</div>
                                        <div class="time-value">{{ funcionario.jornada_intervalo_saida or '--:--' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="schedule-item">
                                <div class="schedule-title">Configurações</div>
                                <div class="field-grid">
                                    <div class="field-item">
                                        <div class="field-label">Turno</div>
                                        <div class="field-value">{{ funcionario.tipo_jornada or 'Não definido' }}</div>
                                    </div>
                                    <div class="field-item">
                                        <div class="field-label">Tolerância</div>
                                        <div class="field-value">{{ funcionario.tolerancia_entrada_minutos or '0' }} minutos</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- EPIs -->
        <div class="col-lg-4">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-hard-hat"></i>
                        EPIs Ativos
                    </h5>
                </div>
                <div class="card-body-modern">
                    {% if funcionario.epis %}
                        {% for epi in funcionario.epis %}
                        <div class="epi-item">
                            <div class="epi-header">
                                <div class="epi-info">
                                    <h6>{{ epi.epi_nome }}</h6>
                                    <div class="epi-ca">CA: {{ epi.epi_ca or 'N/A' }}</div>
                                </div>
                                <div class="epi-status">
                                    {% if epi.epi_data_validade %}
                                        <span class="status-badge status-valid">Válido</span>
                                    {% else %}
                                        <span class="status-badge status-no-date">Sem validade</span>
                                    {% endif %}
                                </div>
                            </div>
                            {% if epi.epi_data_entrega %}
                            <div class="epi-delivery">
                                Entregue em: {{ epi.epi_data_entrega | format_date }}
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="epi-empty">
                            <i class="fas fa-hard-hat"></i>
                            <p>Nenhum EPI cadastrado</p>
                            {% if current_user.is_admin %}
                            <small>Edite o funcionário para adicionar EPIs</small>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Address Section (if available) -->
    {% if funcionario.endereco_rua or funcionario.endereco_cidade %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="modern-card">
                <div class="card-header-modern">
                    <h5 class="card-title-modern">
                        <i class="fas fa-map-marker-alt"></i>
                        Endereço
                    </h5>
                </div>
                <div class="card-body-modern">
                    <div class="field-grid">
                        <div class="field-item">
                            <div class="field-label">Logradouro</div>
                            <div class="field-value">{{ funcionario.endereco_rua or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">CEP</div>
                            <div class="field-value monospace">{{ funcionario.endereco_cep or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Cidade</div>
                            <div class="field-value">{{ funcionario.endereco_cidade or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Estado</div>
                            <div class="field-value">{{ funcionario.endereco_estado or 'Não informado' }}</div>
                        </div>
                        <div class="field-item">
                            <div class="field-label">Bairro</div>
                            <div class="field-value">{{ funcionario.endereco_bairro or 'Não informado' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}