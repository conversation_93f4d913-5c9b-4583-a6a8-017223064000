"""
RLPONTO-WEB v1.0 - Sistema de Controle de Ponto Biométrico Empresarial

Desenvolvido por: AiNexus Tecnologia
Autor: <PERSON> - Full Stack Developer
Versão: 1.0 (Release 2025-01-09)

© 2025 AiNexus Tecnologia. Todos os direitos reservados.
"""

import logging
from datetime import datetime
import json
import os
import re
import time
import pymysql
from pymysql.cursors import DictCursor
from flask import Flask, render_template, request, redirect, url_for, session, jsonify, flash, abort, current_app
from werkzeug.security import generate_password_hash, check_password_hash
import asyncio
import websockets
import requests
import traceback
from flask_cors import CORS, cross_origin  # ✅ IMPORTAÇÃO CORRIGIDA

# Configurar logging primeiro
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('controle-ponto')

# Importações do sistema
from version_info import get_system_banner, get_footer_info, get_interface_info, get_build_info, SYSTEM_INFO, COMPANY_INFO

# Importações dos novos módulos
try:
    from utils.auth import require_login, require_admin, get_current_user, AuthManager, get_template_context
    from utils.database import get_db_connection
    from utils.config import Config  # ✅ NOVO: Importa configurações seguras
    from app_funcionarios import funcionarios_bp
    from app_epis import epis_bp
    from app_registro_ponto import registro_ponto_bp, registro_ponto_underscore_bp  # ✅ ATUALIZADO: Importa ambos blueprints
    from app_relatorios import relatorios_bp  # ✅ NOVO: Blueprint de relatórios
    from app_configuracoes import configuracoes_bp  # ✅ NOVO: Blueprint de configurações
    from app_biometric_config import biometric_config_bp  # ✅ NOVO: Blueprint de configurações biométricas
    from app_horarios_trabalho import horarios_trabalho_bp  # ✅ NOVO: Blueprint de horários
    from app_status import status_bp  # ✅ NOVO: Blueprint de status do sistema
    from app_quality_control import quality_control_bp  # ✅ NOVO: Blueprint de controle de qualidade
    from app_empresa_config import empresa_config_bp  # ✅ NOVO: Blueprint de configuração da empresa
    from app_device_manager import device_manager_bp  # ✅ NOVO: Blueprint de gerenciamento de dispositivos

    # ✅ NOVO: Importar blueprint de banco de horas (controle de jornada)
    try:
        from app_banco_horas import banco_horas_bp
        BANCO_HORAS_DISPONIVEL = True
        logger.info("✅ Blueprint banco_horas importado - Controle de jornada ativo!")
    except ImportError as e:
        BANCO_HORAS_DISPONIVEL = False
        logger.warning(f"⚠️ Blueprint banco_horas não disponível: {e}")

    # ✅ NOVO: Importar blueprint de empresas
    try:
        from app_empresas import empresas_bp
        EMPRESAS_DISPONIVEL = True
        logger.info("✅ Blueprint empresas importado - Gestão de empresas ativa!")
    except ImportError as e:
        EMPRESAS_DISPONIVEL = False
        logger.warning(f"⚠️ Blueprint empresas não disponível: {e}")

    # ✅ NOVO: Importar blueprint de empresa principal
    try:
        from app_empresa_principal import empresa_principal_bp
        EMPRESA_PRINCIPAL_DISPONIVEL = True
        logger.info("✅ Blueprint empresa_principal importado - Gestão de empresa principal ativa!")
    except ImportError as e:
        EMPRESA_PRINCIPAL_DISPONIVEL = False
        logger.warning(f"⚠️ Blueprint empresa_principal não disponível: {e}")

    logger.info("✅ Todos os módulos importados com sucesso")
    
except ImportError as e:
    # ❌ PROBLEMA CRÍTICO: Algum módulo não foi encontrado
    print(f"❌ ERRO CRÍTICO: Falha ao importar módulos necessários: {e}")
    import sys
    import traceback
    traceback.print_exc()
    
    # Configuração de logging básica se config não carregou
    logging.basicConfig(
        level=logging.ERROR,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger('controle-ponto.fallback')
    logger.error(f"Falha crítica na importação: {e}")
    
    # Carrega configurações de fallback
    class Config:
        SECRET_KEY = os.getenv('SECRET_KEY', 'fallback-secret-key-development-only')
        FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
        FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
        FLASK_PORT = int(os.getenv('FLASK_PORT', 5000))
        SESSION_LIFETIME = int(os.getenv('SESSION_LIFETIME', 86400))
        MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))
        LOG_DIR = os.getenv('LOG_DIR', os.path.join(os.path.dirname(__file__), 'logs'))
        LOG_LEVEL = os.getenv('LOG_LEVEL', 'DEBUG')

# Configuração de logging
try:
    os.makedirs(Config.LOG_DIR, exist_ok=True)
    logging.basicConfig(
        filename=f'{Config.LOG_DIR}/app.log',
        level=getattr(logging, Config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger('controle-ponto')
    logger.info("✅ Sistema de logs inicializado com sucesso")
except Exception as e:
    print(f"Erro ao configurar logging: {e}")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger('controle-ponto')
    logger.warning("⚠️ Usando configuração de log padrão")

app = Flask(__name__)

# ✅ CONFIGURAÇÃO SEGURA - Remove credenciais hardcoded
app.secret_key = Config.SECRET_KEY
app.config['PERMANENT_SESSION_LIFETIME'] = Config.SESSION_LIFETIME
app.config['MAX_CONTENT_LENGTH'] = Config.MAX_CONTENT_LENGTH

# ✅ CONFIGURAÇÃO GLOBAL DO CORS - Baseado em Flask-CORS 4.0.0
# Configuração robusta para resolver problemas de cross-origin
CORS(app, 
    resources={
        r"/api/*": {
            "origins": ["http://localhost:3000", "http://127.0.0.1:3000", "http://************", "http://localhost:5000"],
            "methods": ["GET", "HEAD", "POST", "OPTIONS", "PUT", "PATCH", "DELETE"],
            "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "X-Security-Hash", "X-Timestamp"],
            "expose_headers": ["Content-Range", "X-Content-Range"],
            "supports_credentials": True,
            "max_age": 3600
        },
        r"/zkagent/*": {
            "origins": "*",
            "methods": ["GET", "HEAD", "POST", "OPTIONS", "PUT", "PATCH", "DELETE"],
            "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"],
            "supports_credentials": False,
            "max_age": 3600
        },
        r"/*": {
            "origins": ["http://localhost:3000", "http://127.0.0.1:3000", "http://************", "http://localhost:5000"],
            "methods": ["GET", "HEAD", "POST", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"],
            "supports_credentials": True,
            "max_age": 3600
        }
    },
    vary_header=True,
    send_wildcard=False
)

logger.info("✅ CORS configurado globalmente para todas as rotas")
logger.info("🔗 Origens permitidas: localhost:3000, 127.0.0.1:3000, ************, localhost:5000")
logger.info("🛡️ Configuração de segurança: Credentials suportados para APIs principais")

# Mostrar banner do sistema
print(get_system_banner())
logger.info("🚀 Aplicação inicializada com configurações seguras")
logger.info(f"📊 Sistema: {SYSTEM_INFO['name']} v{SYSTEM_INFO['version']}")
logger.info(f"🏢 Desenvolvido por: {COMPANY_INFO['name']} - {COMPANY_INFO['developer']}")
logger.info(f"📅 Build: {SYSTEM_INFO['build']} ({SYSTEM_INFO['release_date']})")

# ✅ NOVO: Handlers de erro robustos
@app.errorhandler(404)
def handle_404_error(e):
    """Handler para erros 404 - Página não encontrada"""
    logger.warning(f"Erro 404: {request.url} - {e}")
    return render_template('erro.html', 
                         titulo="Página não encontrada",
                         mensagem="A página que você está procurando não existe.",
                         codigo=404), 404

@app.errorhandler(403)
def handle_403_error(e):
    """Handler para erros 403 - Acesso negado"""
    logger.warning(f"Erro 403: {request.url} - Usuário: {session.get('usuario', 'Anônimo')}")
    return render_template('erro.html',
                         titulo="Acesso negado",
                         mensagem="Você não tem permissão para acessar esta página.",
                         codigo=403), 403

@app.errorhandler(500)
def handle_500_error(e):
    """Handler para erros 500 - Erro interno do servidor"""
    error_id = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Log detalhado do erro
    logger.error(f"ERRO 500 [{error_id}]: {str(e)}")
    logger.error(f"URL: {request.url}")
    logger.error(f"Método: {request.method}")
    logger.error(f"Usuário: {session.get('usuario', 'Não logado')}")
    logger.error(f"Traceback completo:\n{traceback.format_exc()}")
    
    # Em modo debug, mostra traceback completo
    if Config.FLASK_DEBUG:
        return f"""
        <h1>Erro Interno do Servidor (DEBUG MODE)</h1>
        <h2>ID do Erro: {error_id}</h2>
        <h3>Detalhes:</h3>
        <pre>{traceback.format_exc()}</pre>
        <h3>Request Info:</h3>
        <p><strong>URL:</strong> {request.url}</p>
        <p><strong>Método:</strong> {request.method}</p>
        <p><strong>Usuário:</strong> {session.get('usuario', 'Não logado')}</p>
        <p><strong>Session:</strong> {dict(session)}</p>
        """, 500
    
    # Em produção, mostra erro amigável
    return render_template('erro.html',
                         titulo="Erro interno do servidor",
                         mensagem=f"Ocorreu um erro interno. ID do erro: {error_id}",
                         codigo=500), 500

@app.errorhandler(Exception)
def handle_general_exception(e):
    """Handler geral para exceções não capturadas"""
    error_id = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Log detalhado da exceção
    logger.error(f"EXCEÇÃO NÃO TRATADA [{error_id}]: {str(e)}")
    logger.error(f"Tipo: {type(e).__name__}")
    logger.error(f"URL: {request.url}")
    logger.error(f"Método: {request.method}")
    logger.error(f"Usuário: {session.get('usuario', 'Não logado')}")
    logger.error(f"Traceback completo:\n{traceback.format_exc()}")
    
    # Se é uma requisição AJAX, retorna JSON
    if request.is_json or 'application/json' in request.headers.get('Accept', ''):
        return jsonify({
            'success': False,
            'error': 'Erro interno do servidor',
            'error_id': error_id,
            'message': str(e) if Config.FLASK_DEBUG else 'Erro interno'
        }), 500
    
    # Em modo debug, mostra traceback completo
    if Config.FLASK_DEBUG:
        return f"""
        <h1>Exceção Não Tratada (DEBUG MODE)</h1>
        <h2>ID do Erro: {error_id}</h2>
        <h3>Tipo: {type(e).__name__}</h3>
        <h3>Mensagem: {str(e)}</h3>
        <h3>Traceback:</h3>
        <pre>{traceback.format_exc()}</pre>
        <h3>Request Info:</h3>
        <p><strong>URL:</strong> {request.url}</p>
        <p><strong>Método:</strong> {request.method}</p>
        <p><strong>Usuário:</strong> {session.get('usuario', 'Não logado')}</p>
        <p><strong>Session:</strong> {dict(session)}</p>
        """, 500
    
    # Em produção, mostra erro amigável
    return render_template('erro.html',
                         titulo="Erro interno do sistema",
                         mensagem=f"Ocorreu um erro inesperado. ID: {error_id}",
                         codigo=500), 500

# Função de teste de conexão do banco
def test_database_connection():
    """Testa a conexão com o banco de dados na inicialização"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        conn.close()
        logger.info("✅ Conexão com banco de dados testada com sucesso")
        return True
    except Exception as e:
        logger.error(f"❌ Falha na conexão com banco de dados: {e}")
        return False

# Teste de conexão na inicialização
try:
    if test_database_connection():
        logger.info("✅ Banco de dados acessível")
    else:
        logger.error("❌ Banco de dados inacessível - sistema pode apresentar problemas")
except Exception as e:
    logger.error(f"❌ Erro ao testar conexão: {e}")

# Registro dos Blueprints com tratamento de erro
try:
    app.register_blueprint(funcionarios_bp)
    logger.info("✅ Blueprint funcionarios registrado")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint funcionarios: {e}")

try:
    app.register_blueprint(epis_bp)
    logger.info("✅ Blueprint epis registrado")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint epis: {e}")

try:
    app.register_blueprint(registro_ponto_bp)
    logger.info("✅ Blueprint registro_ponto registrado")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint registro_ponto: {e}")

try:
    app.register_blueprint(registro_ponto_underscore_bp)
    logger.info("✅ Blueprint registro_ponto_underscore registrado (compatibilidade URLs)")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint registro_ponto_underscore: {e}")

try:
    app.register_blueprint(relatorios_bp)
    logger.info("✅ Blueprint relatorios registrado")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint relatorios: {e}")

try:
    app.register_blueprint(configuracoes_bp)
    logger.info("✅ Blueprint configuracoes registrado")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint configuracoes: {e}")

try:
    app.register_blueprint(biometric_config_bp)
    logger.info("✅ Blueprint biometric_config registrado")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint biometric_config: {e}")

try:
    app.register_blueprint(horarios_trabalho_bp)
    logger.info("✅ Blueprint horarios_trabalho registrado")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint horarios_trabalho: {e}")

try:
    app.register_blueprint(status_bp)
    logger.info("✅ Blueprint status registrado")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint status: {e}")

try:
    app.register_blueprint(quality_control_bp)
    logger.info("✅ Blueprint quality_control registrado - Sistema Anti-Regressão ativo!")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint quality_control: {e}")

try:
    app.register_blueprint(empresa_config_bp)
    logger.info("✅ Blueprint empresa_config registrado - Configuração da empresa conforme MCP!")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint empresa_config: {e}")

try:
    app.register_blueprint(device_manager_bp)
    logger.info("✅ Blueprint device_manager registrado - Gerenciamento de dispositivos conforme MCP!")
except Exception as e:
    logger.error(f"❌ Erro ao registrar blueprint device_manager: {e}")

# ✅ NOVO: Registrar blueprint de banco de horas
if BANCO_HORAS_DISPONIVEL:
    try:
        app.register_blueprint(banco_horas_bp)
        logger.info("✅ Blueprint banco_horas registrado - Sistema de controle de jornada ativo!")
    except Exception as e:
        logger.error(f"❌ Erro ao registrar blueprint banco_horas: {e}")

# ✅ NOVO: Registrar blueprint de empresas
if EMPRESAS_DISPONIVEL:
    try:
        app.register_blueprint(empresas_bp)
        logger.info("✅ Blueprint empresas registrado - Gestão de empresas ativa!")
    except Exception as e:
        logger.error(f"❌ Erro ao registrar blueprint empresas: {e}")

# ✅ NOVO: Registrar blueprint de empresa principal
if EMPRESA_PRINCIPAL_DISPONIVEL:
    try:
        app.register_blueprint(empresa_principal_bp)
        logger.info("✅ Blueprint empresa_principal registrado - Gestão de empresa principal ativa!")
    except Exception as e:
        logger.error(f"❌ Erro ao registrar blueprint empresa_principal: {e}")

# Context processor para disponibilizar informações de versão em todos os templates
@app.context_processor
def inject_version_info():
    """Injeta informações de versão e créditos em todos os templates"""
    return {
        'system_info': SYSTEM_INFO,
        'company_info': COMPANY_INFO,
        'footer_info': get_footer_info(),
        'interface_info': get_interface_info()
    }

# Constantes
ADMIN_DEFAULT_USERNAME = "admin"

# Função para verificar se a senha é um hash
def is_password_hash(password):
    return ":" in password and len(password) > 50

# Função para obter a próxima matrícula disponível
def get_next_matricula():
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT MAX(CAST(matricula_empresa AS UNSIGNED)) as max_matricula FROM funcionarios")
            result = cursor.fetchone()
            
            if result['max_matricula'] is None:
                next_num = 1
            else:
                next_num = result['max_matricula'] + 1
                
            return f"{next_num:04d}"
    except Exception as e:
        logger.error(f"Erro ao obter próxima matrícula: {e}")
        return "0001"
    finally:
        if conn:
            conn.close()

# Middleware para verificar autenticação e forçar troca de senha
@app.before_request
def check_auth_and_force_password_change():
    public_routes = ['login', 'static', 'trocar_senha_obrigatoria', 'zkagent_proxy', 'zkagent_options', 'zkagent_status', 'configuracoes.api_test_bridge_public']
    status_routes = ['status.dashboard', 'status.refresh_data', 'status.logout']
    # ✅ CORREÇÃO: Expandir qa_routes para incluir todas as APIs e funcionalidades necessárias
    # ✅ CORREÇÃO: Usuário 'admin' deve ter acesso TOTAL ao sistema
    admin_routes = [
        # ✅ TODAS as rotas de configurações
        'configuracoes.index', 'configuracoes.listar_empresas', 'configuracoes.nova_empresa',
        'configuracoes.salvar_empresa', 'configuracoes.editar_empresa', 'configuracoes.excluir_empresa',
        'configuracoes.api_listar_empresas', 'configuracoes.api_criar_empresa', 'configuracoes.api_validar_cnpj',
        'configuracoes.api_backup', 'configuracoes.api_detectar_dispositivos', 'configuracoes.api_test_bridge_public',
        'configuracoes.api_testar_dispositivo',
        
        # Quality Control - acesso total
        'quality_control.dashboard', 'quality_control.qa_logout', 
        'quality_control.api_run_check', 'quality_control.api_create_backup', 'quality_control.api_get_stats',
        'quality_control.api_detailed_check', 'quality_control.api_backup_list', 'quality_control.api_system_info',
        'quality_control.api_restore_options', 'quality_control.api_restore_files', 'quality_control.api_restore_database',
        
        # APIs de relatórios - acesso total
        'relatorios.api_buscar_registros', 'relatorios.api_exportar_csv', 'relatorios.api_dados_graficos',
        'relatorios.api_resumo_funcionario', 'relatorios.index', 'relatorios.pontos_por_funcionario', 'relatorios.estatisticas',
        
        # APIs de registro de ponto - acesso total
        'registro_ponto.api_registrar_biometrico', 'registro_ponto.api_registrar_manual', 'registro_ponto.api_obter_horarios',
        'registro_ponto.index', 'registro_ponto.registro_biometrico', 'registro_ponto.registro_manual',
        
        # APIs de funcionários - acesso total
        'funcionarios.api_get_funcionario', 'funcionarios.index', 'funcionarios.adicionar', 'funcionarios.detalhes', 'funcionarios.editar',

        # ✅ NOVO: Rotas de empresa principal - acesso total
        'empresa_principal.index', 'empresa_principal.teste', 'empresa_principal.definir_principal',
        'empresa_principal.clientes', 'empresa_principal.listar_clientes', 'empresa_principal.adicionar_cliente',
        'empresa_principal.detalhes_cliente', 'empresa_principal.editar_cliente', 'empresa_principal.alterar_status_cliente',
        'empresa_principal.alocacoes', 'empresa_principal.alocar_funcionario_route', 'empresa_principal.alterar_status_alocacao',
        'empresa_principal.jornadas_clientes', 'empresa_principal.criar_jornada_cliente', 'empresa_principal.aplicar_jornada_cliente',
        'empresa_principal.aplicar_jornadas_automaticamente', 'empresa_principal.definir_jornada_padrao',
        'empresa_principal.relatorios', 'empresa_principal.dados_graficos', 'empresa_principal.gerar_relatorio',
        'empresa_principal.get_funcionarios_disponiveis', 'empresa_principal.get_jornadas_disponiveis',
        'empresa_principal.api_empresa_principal',

        # Outras rotas essenciais
        'index', 'logout', 'configurar_usuarios', 'adicionar_usuario', 'trocar_senha', 'alterar_nivel', 'excluir_usuario'
    ]
    
    qa_routes = [
        # Quality Control rotas principais
        'quality_control.dashboard', 'quality_control.qa_logout', 
        'quality_control.api_run_check', 'quality_control.api_create_backup', 'quality_control.api_get_stats',
        'quality_control.api_detailed_check', 'quality_control.api_backup_list', 'quality_control.api_system_info',
        
        # ✅ NOVAS APIs de restore implementadas
        'quality_control.api_restore_options', 'quality_control.api_restore_files', 'quality_control.api_restore_database',
        
        # APIs de relatórios
        'relatorios.api_buscar_registros', 'relatorios.api_exportar_csv', 'relatorios.api_dados_graficos',
        'relatorios.api_resumo_funcionario', 'relatorios.index', 'relatorios.pontos_por_funcionario', 'relatorios.estatisticas',
        
        # APIs de configurações
        'configuracoes.api_validar_cnpj', 'configuracoes.index',
        
        # APIs de registro de ponto
        'registro_ponto.api_registrar_biometrico', 'registro_ponto.api_registrar_manual', 'registro_ponto.api_obter_horarios',
        'registro_ponto.index', 'registro_ponto.registro_biometrico', 'registro_ponto.registro_manual',
        
        # APIs de funcionários
        'funcionarios.api_get_funcionario', 'funcionarios.index', 'funcionarios.adicionar', 'funcionarios.detalhes', 'funcionarios.editar',
        
        # Outras rotas essenciais
        'index', 'logout'
    ]
    
    if request.endpoint not in public_routes and 'usuario' not in session:
        return redirect(url_for('login'))
    
    # Se for usuário "status", permitir apenas rotas de status
    if 'usuario' in session and session['usuario'] == 'status':
        if request.endpoint not in status_routes and request.endpoint not in public_routes:
            return redirect(url_for('status.dashboard'))
    
    # ✅ CORREÇÃO: Se for usuário "admin", permitir acesso total às rotas administrativas
    elif 'usuario' in session and session['usuario'] == 'admin':
        if request.endpoint not in admin_routes and request.endpoint not in public_routes:
            logger.warning(f"Admin tentou acessar rota não autorizada: {request.endpoint}")
            # Admin tem acesso total, então não bloquear
            pass  # ✅ ADMIN TEM ACESSO TOTAL
    
    # Se for usuário "cavalcrod", permitir apenas rotas de Quality Control
    elif 'usuario' in session and session['usuario'] == 'cavalcrod':
        if request.endpoint not in qa_routes and request.endpoint not in public_routes:
            return redirect(url_for('quality_control.dashboard'))
    
    # Se o usuário está logado e precisa trocar a senha, redireciona
    if (request.endpoint not in public_routes and 
        'force_password_change' in session and 
        session['force_password_change']):
        return redirect(url_for('trocar_senha_obrigatoria'))

# Rota para a página inicial - redirecionada para lista de funcionários
@app.route('/')
@require_login
def index():
    return redirect(url_for('funcionarios.index'))

# Rota para login
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        usuario = request.form.get('usuario')
        senha = request.form.get('senha')
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM usuarios WHERE usuario = %s", (usuario,))
                user = cursor.fetchone()
            
            if not user:
                return render_template('login.html', erro="Usuário ou senha incorretos")

            # Verifica se a senha no banco é texto plano ou hash
            senha_db = user['senha']
            senha_valida = False
            force_password_change = False

            if is_password_hash(senha_db):
                # Senha é um hash, verificar com check_password_hash
                senha_valida = check_password_hash(senha_db, senha)
            else:
                # Senha é texto plano, comparar diretamente
                senha_valida = senha_db == senha
                if senha_valida:
                    logger.info(f"Senha em texto plano detectada para o usuário {usuario}. Forçando troca de senha.")
                    force_password_change = True

            if senha_valida:
                # Busca o nível de acesso na tabela permissoes
                try:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT nivel_acesso FROM permissoes WHERE usuario_id = %s", (user['id'],))
                        permissao = cursor.fetchone()
                    nivel_acesso = permissao['nivel_acesso'] if permissao else 'usuario'
                except Exception as e:
                    logger.error(f"Erro ao buscar nível de acesso: {e}")
                    nivel_acesso = 'usuario'
                
                # Usar o AuthManager para fazer login
                AuthManager.login_user(user, nivel_acesso, force_password_change)
                
                if force_password_change:
                    return redirect(url_for('trocar_senha_obrigatoria'))
                
                # Se for o usuário "status", redirecionar para dashboard de status
                if usuario == 'status':
                    return redirect(url_for('status.dashboard'))
                
                # Se for o usuário "cavalcrod", redirecionar para dashboard de Quality Control
                if usuario == 'cavalcrod':
                    return redirect(url_for('quality_control.dashboard'))
                
                return redirect(url_for('index'))
            else:
                return render_template('login.html', erro="Usuário ou senha incorretos")
        except Exception as e:
            logger.error(f"Erro no login: {e}")
            return render_template('login.html', erro="Erro ao processar login")
        finally:
            if conn:
                conn.close()
    
    return render_template('login.html')

# Rota para página de troca de senha obrigatória
@app.route('/trocar_senha_obrigatoria', methods=['GET', 'POST'])
def trocar_senha_obrigatoria():
    if 'usuario' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        nova_senha = request.form.get('nova_senha')
        
        if not nova_senha:
            return render_template('trocar_senha_obrigatoria.html', erro="Nova senha é obrigatória")
        
        if len(nova_senha) < 8:
            return render_template('trocar_senha_obrigatoria.html', erro="A nova senha deve ter pelo menos 8 caracteres")

        # Gera o hash da nova senha
        senha_hash = generate_password_hash(nova_senha)
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("UPDATE usuarios SET senha = %s WHERE usuario = %s", (senha_hash, session['usuario']))
                conn.commit()
            
            logger.info(f"Senha do usuário {session['usuario']} atualizada para hash com sucesso.")
            session['force_password_change'] = False
            return redirect(url_for('index'))
        except Exception as e:
            logger.error(f"Erro ao atualizar senha: {e}")
            return render_template('trocar_senha_obrigatoria.html', erro="Erro ao atualizar senha")
        finally:
            if conn:
                conn.close()

    return render_template('trocar_senha_obrigatoria.html')

# Rota para logout
@app.route('/logout')
def logout():
    AuthManager.logout_user()
    return redirect(url_for('login'))

# Rota para configurar usuários
@app.route('/configurar_usuarios', methods=['GET', 'POST'])
def configurar_usuarios():
    if session.get('nivel_acesso') != 'admin':
        return redirect(url_for('index'))
    
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT u.*, p.nivel_acesso 
                FROM usuarios u 
                LEFT JOIN permissoes p ON u.id = p.usuario_id 
                ORDER BY u.id
            """)
            usuarios = cursor.fetchall()
        
        # Usar get_template_context para incluir current_user
        context = get_template_context()
        context.update({
            'usuarios': usuarios,
            'admin_default_username': ADMIN_DEFAULT_USERNAME
        })
        
        return render_template('configurar_usuarios.html', **context)
    except Exception as e:
        logger.error(f"Erro na rota configurar_usuarios: {e}")
        return render_template('erro.html', mensagem="Erro ao carregar usuários")
    finally:
        if conn:
            conn.close()

# Rota para adicionar novo usuário
@app.route('/adicionar_usuario', methods=['POST'])
def adicionar_usuario():
    if session.get('nivel_acesso') != 'admin':
        return redirect(url_for('index'))
    
    try:
        usuario = request.form.get('usuario')
        senha = request.form.get('senha')
        nivel_acesso = request.form.get('nivel_acesso')
        
        if not usuario or not senha or not nivel_acesso:
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                
                context = get_template_context()
                context.update({
                    'usuarios': usuarios,
                    'admin_default_username': ADMIN_DEFAULT_USERNAME,
                    'error_message': "Todos os campos são obrigatórios"
                })
                return render_template('configurar_usuarios.html', **context)
            finally:
                if conn:
                    conn.close()
        
        if len(senha) < 8:
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                
                context = get_template_context()
                context.update({
                    'usuarios': usuarios,
                    'admin_default_username': ADMIN_DEFAULT_USERNAME,
                    'error_message': "A senha deve ter pelo menos 8 caracteres"
                })
                return render_template('configurar_usuarios.html', **context)
            finally:
                if conn:
                    conn.close()
        
        if nivel_acesso not in ['admin', 'usuario']:
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                
                context = get_template_context()
                context.update({
                    'usuarios': usuarios,
                    'admin_default_username': ADMIN_DEFAULT_USERNAME,
                    'error_message': "Nível de acesso inválido"
                })
                return render_template('configurar_usuarios.html', **context)
            finally:
                if conn:
                    conn.close()
        
        # Gera o hash da senha
        senha_hash = generate_password_hash(senha)
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                # Insere o novo usuário na tabela usuarios
                cursor.execute("INSERT INTO usuarios (usuario, senha) VALUES (%s, %s)", (usuario, senha_hash))
                usuario_id = cursor.lastrowid
                # Insere o nível de acesso na tabela permissoes
                cursor.execute("INSERT INTO permissoes (usuario_id, nivel_acesso) VALUES (%s, %s)", (usuario_id, nivel_acesso))
                conn.commit()
            
            logger.info(f"Novo usuário {usuario} adicionado com sucesso.")
            
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                
                context = get_template_context()
                context.update({
                    'usuarios': usuarios,
                    'admin_default_username': ADMIN_DEFAULT_USERNAME,
                    'message': "Usuário adicionado com sucesso"
                })
                return render_template('configurar_usuarios.html', **context)
            finally:
                if conn:
                    conn.close()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Erro ao adicionar usuário: {e}")
            conn = None
            try:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT u.*, p.nivel_acesso 
                        FROM usuarios u 
                        LEFT JOIN permissoes p ON u.id = p.usuario_id 
                        ORDER BY u.id
                    """)
                    usuarios = cursor.fetchall()
                
                context = get_template_context()
                context.update({
                    'usuarios': usuarios,
                    'admin_default_username': ADMIN_DEFAULT_USERNAME,
                    'error_message': f"Erro ao adicionar usuário: {str(e)}"
                })
                return render_template('configurar_usuarios.html', **context)
            finally:
                if conn:
                    conn.close()
    except Exception as e:
        logger.error(f"Erro geral ao adicionar usuário: {e}")
        return render_template('erro.html', mensagem=f"Erro ao processar cadastro de usuário: {str(e)}")

# Rota para trocar senha de usuário
@app.route('/trocar_senha', methods=['POST'])
def trocar_senha():
    if session.get('nivel_acesso') != 'admin':
        return jsonify({'success': False, 'message': 'Acesso negado'})
    
    try:
        usuario_id = request.form.get('id')
        nova_senha = request.form.get('senha')
        
        if not usuario_id or not nova_senha:
            return jsonify({'success': False, 'message': 'Dados incompletos'})
        
        if len(nova_senha) < 8:
            return jsonify({'success': False, 'message': 'A senha deve ter pelo menos 8 caracteres'})
        
        senha_hash = generate_password_hash(nova_senha)
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT usuario FROM usuarios WHERE id = %s", (usuario_id,))
                user_result = cursor.fetchone()
                if not user_result:
                    return jsonify({'success': False, 'message': 'Usuário não encontrado'})
                
                username = user_result['usuario']
                
                cursor.execute("UPDATE usuarios SET senha = %s WHERE id = %s", (senha_hash, usuario_id))
                conn.commit()
                
                kick_user = username == session.get('usuario')
                
                return jsonify({
                    'success': True, 
                    'message': 'Senha alterada com sucesso',
                    'kick_user': kick_user
                })
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Erro ao trocar senha: {e}")
            return jsonify({'success': False, 'message': f'Erro ao trocar senha: {str(e)}'})
        finally:
            if conn:
                conn.close()
    except Exception as e:
        logger.error(f"Erro geral ao trocar senha: {e}")
        return jsonify({'success': False, 'message': f'Erro: {str(e)}'})

# Rota para alterar nível de acesso
@app.route('/alterar_nivel', methods=['POST'])
def alterar_nivel():
    if session.get('nivel_acesso') != 'admin':
        return jsonify({'success': False, 'message': 'Acesso negado'})
    
    try:
        usuario_id = request.form.get('id')
        novo_nivel = request.form.get('nivel')
        
        if not usuario_id or not novo_nivel:
            return jsonify({'success': False, 'message': 'Dados incompletos'})
        
        if novo_nivel not in ['admin', 'usuario']:
            return jsonify({'success': False, 'message': 'Nível de acesso inválido'})
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT usuario FROM usuarios WHERE id = %s", (usuario_id,))
                user_result = cursor.fetchone()
                if not user_result:
                    return jsonify({'success': False, 'message': 'Usuário não encontrado'})
                
                username = user_result['usuario']
                
                if username == ADMIN_DEFAULT_USERNAME and novo_nivel != 'admin':
                    return jsonify({
                        'success': False, 
                        'message': 'Não é permitido alterar o nível de acesso do usuário admin padrão'
                    })
                
                # Verificar se o usuário tem registro na tabela permissoes
                cursor.execute("SELECT nivel_acesso FROM permissoes WHERE usuario_id = %s", (usuario_id,))
                permissao_existente = cursor.fetchone()

                if permissao_existente:
                    # Usuário já tem permissão - fazer UPDATE
                    cursor.execute("UPDATE permissoes SET nivel_acesso = %s WHERE usuario_id = %s", (novo_nivel, usuario_id))
                    rows_affected = cursor.rowcount

                    if rows_affected > 0:
                        conn.commit()
                        logger.info(f"Nível de acesso alterado: usuário {username} (ID: {usuario_id}) -> {novo_nivel}")
                        return jsonify({'success': True, 'message': 'Nível de acesso alterado com sucesso'})
                    else:
                        return jsonify({'success': False, 'message': 'Erro: Nenhuma linha foi afetada na atualização'})
                else:
                    # Usuário não tem permissão - criar registro
                    cursor.execute("INSERT INTO permissoes (usuario_id, nivel_acesso, data_atribuicao) VALUES (%s, %s, NOW())", (usuario_id, novo_nivel))
                    conn.commit()
                    logger.info(f"Permissão criada: usuário {username} (ID: {usuario_id}) -> {novo_nivel}")
                    return jsonify({'success': True, 'message': 'Nível de acesso definido com sucesso'})
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Erro ao alterar nível de acesso para usuário ID {usuario_id}: {e}")
            return jsonify({'success': False, 'message': f'Erro ao alterar nível de acesso: {str(e)}'})
        finally:
            if conn:
                conn.close()
    except Exception as e:
        logger.error(f"Erro geral ao alterar nível de acesso: {e}")
        return jsonify({'success': False, 'message': f'Erro: {str(e)}'})

# Rota para excluir usuário
@app.route('/excluir_usuario', methods=['POST'])
def excluir_usuario():
    if session.get('nivel_acesso') != 'admin':
        return jsonify({'success': False, 'message': 'Acesso negado'})
    
    try:
        usuario_id = request.form.get('id')
        
        if not usuario_id:
            return jsonify({'success': False, 'message': 'ID de usuário não fornecido'})
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT usuario FROM usuarios WHERE id = %s", (usuario_id,))
                user_result = cursor.fetchone()
                if not user_result:
                    return jsonify({'success': False, 'message': 'Usuário não encontrado'})
                
                username = user_result['usuario']
                
                if username == ADMIN_DEFAULT_USERNAME:
                    return jsonify({
                        'success': False, 
                        'message': 'Não é permitido excluir o usuário admin padrão'
                    })
                
                cursor.execute("DELETE FROM permissoes WHERE usuario_id = %s", (usuario_id,))
                cursor.execute("DELETE FROM usuarios WHERE id = %s", (usuario_id,))
                conn.commit()
                
                return jsonify({'success': True, 'message': 'Usuário excluído com sucesso'})
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Erro ao excluir usuário: {e}")
            return jsonify({'success': False, 'message': f'Erro ao excluir usuário: {str(e)}'})
        finally:
            if conn:
                conn.close()
    except Exception as e:
        logger.error(f"Erro geral ao excluir usuário: {e}")
        return jsonify({'success': False, 'message': f'Erro: {str(e)}'})

# Função para comparar templates biométricos via WebSocket
async def comparar_templates(template1, template2):
    async with websockets.connect("ws://localhost:8765") as websocket:
        await websocket.send(json.dumps({
            "command": "MATCH",
            "params": {
                "template1": template1,
                "template2": template2
            }
        }))
        response = await websocket.recv()
        data = json.loads(response)
        if data["type"] == "STATUS":
            return data["data"]["score"] >= 55  # Usa o MATCH_THRESHOLD de biometria_service.py
        else:
            raise Exception("Erro ao comparar templates: " + data.get("data", {}).get("message", "Desconhecido"))

# Rota para verificar biometria
@app.route('/api/verificar_biometria', methods=['POST'])
def verificar_biometria():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': 'Dados não fornecidos'})
        
        template = data.get('template')
        if not template:
            return jsonify({'success': False, 'message': 'Template biométrico não fornecido'})
        
        conn = None
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id, nome_completo, digital_dedo1, digital_dedo2 
                    FROM funcionarios 
                    WHERE digital_dedo1 IS NOT NULL OR digital_dedo2 IS NOT NULL
                """)
                funcionarios = cursor.fetchall()
                
                for funcionario in funcionarios:
                    if funcionario['digital_dedo1']:
                        try:
                            match = asyncio.run(comparar_templates(template, funcionario['digital_dedo1']))
                            if match:
                                return jsonify({
                                    'success': False, 
                                    'message': f'Biometria já cadastrada para o funcionário {funcionario["nome_completo"]} (Dedo 1)'
                                })
                        except Exception as e:
                            logger.error(f"Erro ao comparar biometria (dedo 1): {e}")
                            continue
                    
                    if funcionario['digital_dedo2']:
                        try:
                            match = asyncio.run(comparar_templates(template, funcionario['digital_dedo2']))
                            if match:
                                return jsonify({
                                    'success': False, 
                                    'message': f'Biometria já cadastrada para o funcionário {funcionario["nome_completo"]} (Dedo 2)'
                                })
                        except Exception as e:
                            logger.error(f"Erro ao comparar biometria (dedo 2): {e}")
                            continue
                
                return jsonify({'success': True, 'message': 'Biometria não cadastrada anteriormente'})
                
        except Exception as e:
            logger.error(f"Erro ao verificar biometria: {e}")
            return jsonify({'success': False, 'message': f'Erro ao verificar biometria: {str(e)}'})
        finally:
            if conn:
                conn.close()
    except Exception as e:
        logger.error(f"Erro geral na rota /api/verificar_biometria: {e}")
        return jsonify({'success': False, 'message': f'Erro inesperado: {str(e)}'})

# Rotas de proxy para ZKAgent - Resolve problemas de CORS
@app.route('/api/zkagent/<path:endpoint>', methods=['GET', 'POST'])
def zkagent_proxy(endpoint):
    """
    Proxy para comunicação com ZKAgent
    Resolve problemas de CORS entre frontend e ZKAgent
    """
    # Tenta detectar IP do cliente para conectar no ZKAgent correto
    client_ip = request.headers.get('X-Forwarded-For', request.remote_addr)
    
    # Para desenvolvimento local, sempre usa localhost
    if client_ip in ['127.0.0.1', '::1'] or request.host.startswith('localhost'):
        zkagent_url = 'http://localhost:5001'
    else:
        # Em rede, tenta o IP do cliente (onde está o ZKAgent)
        zkagent_url = f'http://{client_ip}:5001'
    
    target_url = f"{zkagent_url}/{endpoint}"
    
    try:
        logger.info(f"Proxy ZKAgent: {request.method} {target_url} (client: {client_ip})")
        
        if request.method == 'GET':
            response = requests.get(target_url, timeout=10)
        elif request.method == 'POST':
            # Repassa dados JSON do frontend
            json_data = request.get_json() if request.is_json else None
            response = requests.post(target_url, json=json_data, timeout=15)
        
        # Retorna a resposta do ZKAgent com headers CORS corretos
        return response.text, response.status_code, {
            'Content-Type': response.headers.get('Content-Type', 'text/plain'),
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
        
    except requests.exceptions.ConnectionError:
        logger.error(f"ZKAgent não conectado em {zkagent_url}")
        return jsonify({
            'error': 'ZKAgent não está respondendo',
            'details': f'Tentou conectar em {zkagent_url}. Verifique se o serviço ZKAgent está rodando no PC cliente',
            'client_ip': client_ip,
            'url': zkagent_url
        }), 503
        
    except requests.exceptions.Timeout:
        logger.error(f"Timeout na comunicação com ZKAgent: {target_url}")
        return jsonify({
            'error': 'Timeout na comunicação com ZKAgent',
            'details': 'ZKAgent demorou muito para responder'
        }), 504
        
    except Exception as e:
        logger.error(f"Erro no proxy ZKAgent: {e}")
        return jsonify({
            'error': 'Erro interno no proxy',
            'details': str(e)
        }), 500

# Rota para configurar CORS preflight
@app.route('/api/zkagent/<path:endpoint>', methods=['OPTIONS'])
def zkagent_options(endpoint):
    """Responde a requisições OPTIONS para CORS preflight"""
    return '', 200, {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }

# Rota para status do ZKAgent via proxy
@app.route('/api/zkagent-status')
def zkagent_status():
    """
    Verifica status completo do ZKAgent
    """
    zkagent_url = 'http://localhost:5001'
    
    try:
        # Testa conexão básica
        test_response = requests.get(f"{zkagent_url}/test", timeout=5)
        test_ok = test_response.status_code == 200
        
        # Verifica dispositivos
        devices_response = requests.get(f"{zkagent_url}/list-devices", timeout=5)
        devices_count = 0
        if devices_response.status_code == 200:
            devices_text = devices_response.text
            match = re.search(r'(\d+)', devices_text)
            devices_count = int(match.group(1)) if match else 0
        
        return jsonify({
            'success': True,
            'zkagent_running': test_ok,
            'devices_count': devices_count,
            'url': zkagent_url,
            'status': 'online' if test_ok else 'offline'
        })
        
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'zkagent_running': False,
            'devices_count': 0,
            'url': zkagent_url,
            'status': 'offline',
            'error': 'ZKAgent não está respondendo'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'zkagent_running': False,
            'devices_count': 0,
            'url': zkagent_url,
            'status': 'error',
            'error': str(e)
        })

@app.errorhandler(413)
def request_entity_too_large(error):
    return render_template('erro_upload.html'), 413

# Rotas para detecção e teste biométrico via bridge local
@app.route('/configuracoes/api/detectar-dispositivos', methods=['GET'])
def api_detectar_dispositivos():
    """
    API para detecção de dispositivos biométricos
    Se comunica diretamente com a bridge local no Windows (localhost:8080)
    """
    try:
        # URL da bridge local
        bridge_url = "http://localhost:8080/api/detect-biometric-devices"
        
        # Fazer requisição à bridge local
        response = requests.get(bridge_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            return jsonify(data)
        else:
            current_app.logger.error(f"Erro na comunicação com bridge: {response.status_code} - {response.text}")
            return jsonify({
                'success': False,
                'message': 'Erro na comunicação com a bridge local',
                'error': f"Status code: {response.status_code}",
                'dispositivos_encontrados': 0,
                'dispositivos': [],
                'debug_info': {
                    'bridge_url': bridge_url,
                    'status_code': response.status_code,
                    'response_text': response.text[:100] + '...' if len(response.text) > 100 else response.text
                }
            }), 500
            
    except requests.exceptions.ConnectionError:
        current_app.logger.error("Falha na conexão com a bridge biométrica local")
        return jsonify({
            'success': False,
            'message': 'Bridge biométrico não encontrado. Verifique se o serviço está rodando em localhost:8080',
            'dispositivos_encontrados': 0,
            'dispositivos': [],
            'debug_info': {
                'error_type': 'ConnectionError',
                'bridge_url': bridge_url
            }
        }), 503
        
    except Exception as e:
        current_app.logger.error(f"Erro ao detectar dispositivos biométricos: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro interno ao detectar dispositivos: {str(e)}',
            'dispositivos_encontrados': 0,
            'dispositivos': [],
            'debug_info': {
                'error': str(e),
                'traceback': traceback.format_exc()
            }
        }), 500

@app.route('/configuracoes/api/teste-captura/<device_id>', methods=['POST'])
def api_teste_captura(device_id):
    """
    API para testar captura biométrica com um dispositivo específico
    Se comunica com a bridge local para verificação real do hardware
    """
    try:
        # Verificar primeiro se o dispositivo existe
        bridge_url = f"http://localhost:8080/api/test-device/{device_id}"
        
        # Fazer requisição à bridge local
        response = requests.post(bridge_url, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            return jsonify(data)
        elif response.status_code == 404:
            return jsonify({
                'success': False,
                'message': 'Dispositivo não encontrado ou não está conectado',
                'error_code': 'DEVICE_NOT_FOUND'
            }), 404
        else:
            return jsonify({
                'success': False,
                'message': f'Falha ao testar dispositivo: erro {response.status_code}',
                'error': response.text
            }), 500
            
    except requests.exceptions.ConnectionError:
        current_app.logger.error("Falha na conexão com a bridge biométrica local")
        return jsonify({
            'success': False,
            'message': 'Bridge biométrico não encontrado. Verifique se o serviço está rodando',
            'error_code': 'BRIDGE_CONNECTION_ERROR'
        }), 503
        
    except Exception as e:
        current_app.logger.error(f"Erro ao testar dispositivo {device_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao comunicar com o dispositivo: {str(e)}',
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # ✅ CONFIGURAÇÃO SEGURA - Remove debug hardcoded
    logger.info(f"🚀 Iniciando servidor em {Config.FLASK_HOST}:{Config.FLASK_PORT}")
    logger.info(f"🐛 Debug mode: {'✅ Habilitado' if Config.FLASK_DEBUG else '❌ Desabilitado'}")
    
    if Config.FLASK_DEBUG:
        logger.warning("⚠️ ATENÇÃO: Modo debug habilitado - apenas para desenvolvimento!")
    
    app.run(
        debug=Config.FLASK_DEBUG,
        host=Config.FLASK_HOST,
        port=Config.FLASK_PORT
    )
