{% extends "base.html" %}

{% block title %}Configurar <PERSON> - <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
    .config-container {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .config-header {
        border-bottom: 2px solid #4fbdba;
        padding-bottom: 15px;
        margin-bottom: 25px;
    }
    
    .config-header h2 {
        color: #495057;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
    }
    
    .section {
        margin-bottom: 40px;
    }
    
    .section-title {
        color: #495057;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .form-container {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 30px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
        font-size: 14px;
    }
    
    .form-group input,
    .form-group select {
        padding: 10px 12px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: white;
    }
    
    .form-group input:focus,
    .form-group select:focus {
        outline: none;
        border-color: #4fbdba;
        box-shadow: 0 0 0 3px rgba(79, 189, 186, 0.1);
    }
    
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-primary {
        background: #4fbdba;
        color: white;
    }
    
    .btn-primary:hover {
        background: #3da8a6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(79, 189, 186, 0.3);
    }
    
    .btn-warning {
        background: #ffc107;
        color: #1a2634;
    }
    
    .btn-warning:hover {
        background: #e0a800;
    }
    
    .btn-danger {
        background: #dc3545;
        color: white;
    }
    
    .btn-danger:hover {
        background: #b52a37;
    }
    
    .users-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .users-table table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .users-table th,
    .users-table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    .users-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
    }
    
    .users-table tbody tr:hover {
        background: #f8f9fa;
    }
    
    .user-actions {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .level-select {
        background: white;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 5px 8px;
        font-size: 12px;
        min-width: 100px;
    }
    
    .level-select:focus {
        outline: none;
        border-color: #4fbdba;
    }
    
    .admin-badge {
        background: #dc3545;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    /* Modal */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .modal-content {
        background: white;
        margin: 10% auto;
        padding: 30px;
        border-radius: 12px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }
    
    .modal-header {
        border-bottom: 2px solid #4fbdba;
        padding-bottom: 15px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-header h3 {
        margin: 0;
        color: #495057;
        font-size: 20px;
    }
    
    .close {
        color: #6c757d;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        border: none;
        background: none;
    }
    
    .close:hover {
        color: #495057;
    }
    
    .modal-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
    
    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .user-actions {
            flex-direction: column;
            gap: 5px;
        }
        
        .config-container {
            padding: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="config-container">
    <div class="config-header">
        <h2>⚙️ Configurar Usuários</h2>
    </div>
    
    <!-- Adicionar Usuário -->
    <div class="section">
        <h3 class="section-title">
            👤 Adicionar Novo Usuário
        </h3>
        
        <div class="form-container">
            <form id="adicionar-usuario-form" method="POST" action="/adicionar_usuario">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="usuario">Nome de Usuário</label>
                        <input type="text" id="usuario" name="usuario" required placeholder="Digite o nome de usuário">
                    </div>
                    
                    <div class="form-group">
                        <label for="senha">Senha</label>
                        <input type="password" id="senha" name="senha" required placeholder="Mínimo 8 caracteres">
                    </div>
                    
                    <div class="form-group">
                        <label for="nivel_acesso">Nível de Acesso</label>
                        <select id="nivel_acesso" name="nivel_acesso" required>
                            <option value="usuario">Usuário</option>
                            <option value="admin">Administrador</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    ✅ Adicionar Usuário
                </button>
            </form>
        </div>
    </div>
    
    <!-- Lista de Usuários -->
    <div class="section">
        <h3 class="section-title">
            👥 Usuários Cadastrados
        </h3>
        
        <div class="users-table">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Usuário</th>
                        <th>Nível de Acesso</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in usuarios %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>
                            <strong>{{ user.usuario }}</strong>
                            {% if user.usuario == admin_default_username %}
                                <span class="admin-badge">Admin Padrão</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.usuario == admin_default_username %}
                                <span class="badge badge-danger">Administrador</span>
                            {% else %}
                                <select class="level-select" 
                                        onchange="alterarNivel({{ user.id }}, this.value)"
                                        data-user-id="{{ user.id }}">
                                    <option value="admin" {% if user.nivel_acesso == 'admin' %}selected{% endif %}>Administrador</option>
                                    <option value="usuario" {% if user.nivel_acesso == 'usuario' %}selected{% endif %}>Usuário</option>
                                </select>
                            {% endif %}
                        </td>
                        <td>
                            <div class="user-actions">
                                <button class="btn btn-warning btn-sm" 
                                        onclick="abrirModalSenha({{ user.id }})" 
                                        type="button">
                                    🔐 Trocar Senha
                                </button>
                                
                                {% if user.usuario != admin_default_username %}
                                <button class="btn btn-danger btn-sm" 
                                        onclick="confirmarExclusaoUsuario({{ user.id }}, {{ user.usuario | tojson | safe }})" 
                                        type="button">
                                    🗑️ Excluir
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal de alteração de senha -->
<div id="modalSenha" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>🔐 Alterar Senha</h3>
            <button class="close" onclick="fecharModalSenha()">&times;</button>
        </div>
        
        <form id="formAlterarSenha">
            <input type="hidden" id="usuarioIdSenha" name="usuarioIdSenha">
            
            <div class="form-group">
                <label for="novaSenha">Nova Senha</label>
                <input type="password" id="novaSenha" name="novaSenha" required placeholder="Mínimo 8 caracteres">
            </div>
            
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="fecharModalSenha()">
                    Cancelar
                </button>
                <button type="submit" class="btn btn-primary">
                    💾 Salvar Senha
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Abrir modal de senha
function abrirModalSenha(userId) {
    document.getElementById('usuarioIdSenha').value = userId;
    document.getElementById('modalSenha').style.display = 'block';
    document.getElementById('novaSenha').focus();
}

// Fechar modal de senha
function fecharModalSenha() {
    document.getElementById('modalSenha').style.display = 'none';
    document.getElementById('formAlterarSenha').reset();
}

// Alterar nível de acesso
function alterarNivel(userId, novoNivel) {
    fetch('/alterar_nivel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${userId}&nivel=${novoNivel}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erro: ' + data.message);
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erro ao alterar nível de acesso');
        location.reload();
    });
}

// Confirmar exclusão de usuário
function confirmarExclusaoUsuario(userId, userName) {
    const message = `Tem certeza que deseja excluir o usuário "${userName}"? Esta ação não poderá ser desfeita.`;
    
    showConfirmModal(message, function() {
        fetch('/excluir_usuario', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${userId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erro ao excluir usuário');
        });
    });
}

// Form de alteração de senha
document.getElementById('formAlterarSenha').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const userId = formData.get('usuarioIdSenha');
    const novaSenha = formData.get('novaSenha');
    
    if (novaSenha.length < 8) {
        alert('A senha deve ter pelo menos 8 caracteres');
        return;
    }
    
    fetch('/trocar_senha', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${userId}&senha=${encodeURIComponent(novaSenha)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            fecharModalSenha();
            alert('Senha alterada com sucesso');
            if (data.kick_user) {
                alert('Você alterou sua própria senha e será deslogado');
                window.location.href = '/logout';
            }
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erro ao alterar senha');
    });
});

// Fechar modal clicando fora
document.getElementById('modalSenha').addEventListener('click', function(e) {
    if (e.target === this) {
        fecharModalSenha();
    }
});

// Validação em tempo real
document.getElementById('novaSenha').addEventListener('input', function(e) {
    const senha = e.target.value;
    const isValid = senha.length >= 8;
    
    if (!isValid && senha.length > 0) {
        e.target.style.borderColor = '#dc3545';
    } else {
        e.target.style.borderColor = '#ced4da';
    }
});
</script>
{% endblock %}